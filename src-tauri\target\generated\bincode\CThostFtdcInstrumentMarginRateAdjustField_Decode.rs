impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInstrumentMarginRateAdjustField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?,
            InvestorRange : :: bincode :: Decode :: decode(decoder) ?,
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, HedgeFlag : :: bincode
            :: Decode :: decode(decoder) ?, LongMarginRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?, LongMarginRatioByVolume :
            :: bincode :: Decode :: decode(decoder) ?, ShortMarginRatioByMoney
            : :: bincode :: Decode :: decode(decoder) ?,
            ShortMarginRatioByVolume : :: bincode :: Decode :: decode(decoder)
            ?, IsRelative : :: bincode :: Decode :: decode(decoder) ?,
            InstrumentID : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInstrumentMarginRateAdjustField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorRange : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, HedgeFlag : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LongMarginRatioByMoney : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LongMarginRatioByVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, ShortMarginRatioByMoney :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ShortMarginRatioByVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IsRelative : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}