impl :: bincode :: Encode for CThostFtdcExchangeOrderActionErrorField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ExchangeID, encoder) ?; ::
        bincode :: Encode :: encode(&self.OrderSysID, encoder) ?; :: bincode
        :: Encode :: encode(&self.TraderID, encoder) ?; :: bincode :: Encode
        :: encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorMsg, encoder) ?; core :: result :: Result :: Ok(())
    }
}