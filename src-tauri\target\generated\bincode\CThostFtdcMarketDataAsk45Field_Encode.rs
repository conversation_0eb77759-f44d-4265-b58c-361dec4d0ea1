impl :: bincode :: Encode for CThostFtdcMarketDataAsk45Field
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.AskPrice4, encoder) ?; :: bincode
        :: Encode :: encode(&self.AskVolume4, encoder) ?; :: bincode :: Encode
        :: encode(&self.AskPrice5, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume5, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}