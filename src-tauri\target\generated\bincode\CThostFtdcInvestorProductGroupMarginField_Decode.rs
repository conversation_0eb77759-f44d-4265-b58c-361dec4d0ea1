impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInvestorProductGroupMarginField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?, BrokerID :
            :: bincode :: Decode :: decode(decoder) ?, InvestorID : :: bincode
            :: Decode :: decode(decoder) ?, TradingDay : :: bincode :: Decode
            :: decode(decoder) ?, SettlementID : :: bincode :: Decode ::
            decode(decoder) ?, FrozenMargin : :: bincode :: Decode ::
            decode(decoder) ?, LongFrozenMargin : :: bincode :: Decode ::
            decode(decoder) ?, ShortFrozenMargin : :: bincode :: Decode ::
            decode(decoder) ?, UseMargin : :: bincode :: Decode ::
            decode(decoder) ?, LongUseMargin : :: bincode :: Decode ::
            decode(decoder) ?, ShortUseMargin : :: bincode :: Decode ::
            decode(decoder) ?, ExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, LongExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, ShortExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, CloseProfit : :: bincode :: Decode ::
            decode(decoder) ?, FrozenCommission : :: bincode :: Decode ::
            decode(decoder) ?, Commission : :: bincode :: Decode ::
            decode(decoder) ?, FrozenCash : :: bincode :: Decode ::
            decode(decoder) ?, CashIn : :: bincode :: Decode ::
            decode(decoder) ?, PositionProfit : :: bincode :: Decode ::
            decode(decoder) ?, OffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, LongOffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, ShortOffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, ExchOffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, LongExchOffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, ShortExchOffsetAmount : :: bincode :: Decode ::
            decode(decoder) ?, HedgeFlag : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?, ProductGroupID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInvestorProductGroupMarginField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradingDay : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, SettlementID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrozenMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LongFrozenMargin : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ShortFrozenMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            UseMargin : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LongUseMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ShortUseMargin : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchMargin : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, LongExchMargin :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ShortExchMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CloseProfit : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrozenCommission : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            Commission : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrozenCash : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, CashIn : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PositionProfit : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OffsetAmount : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LongOffsetAmount : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ShortOffsetAmount : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ExchOffsetAmount : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LongExchOffsetAmount : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, ShortExchOffsetAmount : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, HedgeFlag : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestUnitID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ProductGroupID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}