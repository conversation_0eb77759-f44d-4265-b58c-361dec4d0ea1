impl :: bincode :: Encode for CThostFtdcTransferHeaderField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.Version, encoder) ?; :: bincode
        :: Encode :: encode(&self.TradeCode, encoder) ?; :: bincode :: Encode
        :: encode(&self.TradeDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankBrchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Oper<PERSON>o, encoder) ?; :: bincode :: Encode ::
        encode(&self.DeviceID, encoder) ?; :: bincode :: Encode ::
        encode(&self.RecordNum, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; core :: result :: Result :: Ok(())
    }
}