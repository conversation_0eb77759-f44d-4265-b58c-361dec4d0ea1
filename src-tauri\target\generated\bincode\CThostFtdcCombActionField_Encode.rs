impl :: bincode :: Encode for CThostFtdcCombActionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombActionRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Direction, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ParticipantID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.TraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.NotifySequence, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SequenceNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrontID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserProductInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.StatusMsg, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve3, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.ComTradeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}