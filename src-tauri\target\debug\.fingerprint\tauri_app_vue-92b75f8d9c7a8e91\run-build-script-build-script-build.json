{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 6890311550847060776], [16702348383442838006, "build_script_build", false, 7266411072117742732], [15180317924524316486, "build_script_build", false, 6767923270360115039]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri_app_vue-92b75f8d9c7a8e91\\output", "paths": ["wrapper.hpp", "tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}