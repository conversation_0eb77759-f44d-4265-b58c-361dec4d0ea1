impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcDepositResultInformField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            DepositSeqNo : :: bincode :: Decode :: decode(decoder) ?, BrokerID
            : :: bincode :: Decode :: decode(decoder) ?, InvestorID : ::
            bincode :: Decode :: decode(decoder) ?, Deposit : :: bincode ::
            Decode :: decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?, ReturnCode : :: bincode :: Decode ::
            decode(decoder) ?, DescrInfoForReturnCode : :: bincode :: Decode
            :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcDepositResultInformField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            DepositSeqNo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Deposit : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, RequestID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ReturnCode : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, DescrInfoForReturnCode : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}