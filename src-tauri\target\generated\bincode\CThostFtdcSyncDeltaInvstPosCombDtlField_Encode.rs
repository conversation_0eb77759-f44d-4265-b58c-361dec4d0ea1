impl :: bincode :: Encode for CThostFtdcSyncDeltaInvstPosCombDtlField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.OpenDate, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ComTradeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.HedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.Direction, encoder) ?; :: bincode :: Encode ::
        encode(&self.TotalAmt, encoder) ?; :: bincode :: Encode ::
        encode(&self.Margin, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByMoney, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.LegID, encoder) ?; :: bincode :: Encode ::
        encode(&self.LegMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeGroupID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.SyncDeltaSequenceNo, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}