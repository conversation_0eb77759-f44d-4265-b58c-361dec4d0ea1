# TradingPanel 行情数据清理方案

## 问题描述

在关闭 TradingPanel 时，需要正确断开行情数据获取，避免：
1. 内存泄漏
2. 不必要的网络请求
3. 事件监听器累积
4. 资源浪费

## 解决方案

### 1. 资源管理变量

```typescript
// 行情数据监听器引用，用于在组件卸载时清理
let marketDataListener: ((data: any) => void) | null = null

// 当前订阅的合约代码列表
const subscribedContracts = ref<string[]>([])
```

### 2. 清理函数

```typescript
// 清理当前行情订阅
const cleanupCurrentSubscription = async () => {
  // 移除现有的行情数据监听器
  if (marketDataListener) {
    ctpService.off('market_data', marketDataListener)
    marketDataListener = null
    console.log('✅ 已移除现有行情数据监听器')
  }

  // 取消订阅现有合约
  if (subscribedContracts.value.length > 0) {
    try {
      const result = await ctpService.unsubscribeMarketData(subscribedContracts.value)
      if (result.success) {
        console.log('✅ 已取消订阅现有合约:', subscribedContracts.value)
      } else {
        console.warn('⚠️ 取消订阅现有合约失败:', result.error)
      }
    } catch (error) {
      console.error('❌ 取消订阅现有合约异常:', error)
    }
    subscribedContracts.value = []
  }
}
```

### 3. 订阅管理

```typescript
// 根据合约代码查询行情数据
const queryMarketData = async (contractCode: string): Promise<boolean> => {
  try {
    console.log(`🔍 查询行情数据: ${contractCode}`)

    // 先清理现有订阅
    await cleanupCurrentSubscription()

    // 订阅新的行情数据
    const subscribeResult = await ctpService.subscribeMarketData([contractCode])

    if (subscribeResult.success) {
      console.log('✅ 行情数据订阅成功:', subscribeResult.data)
      message.success(`已订阅 ${contractCode} 行情数据`)

      // 添加到已订阅列表
      if (!subscribedContracts.value.includes(contractCode)) {
        subscribedContracts.value.push(contractCode)
      }

      // 创建行情数据监听器
      marketDataListener = (data: any) => {
        if (data.instrument_id === contractCode) {
          console.log('📈 收到行情数据:', data)
          updateMarketData(data)
        }
      }

      // 监听行情数据更新
      ctpService.on('market_data', marketDataListener)

      return true
    } else {
      console.warn(`⚠️ 订阅行情失败: ${contractCode}`, subscribeResult.error)
      message.warning(`订阅行情失败: ${subscribeResult.error}`)
      return false
    }
  } catch (error) {
    console.error(`❌ 查询行情数据失败: ${contractCode}`, error)
    message.error(`查询行情数据失败: ${error instanceof Error ? error.message : '未知错误'}`)
    return false
  }
}
```

### 4. 合约变化监听

```typescript
// 监听合约变化，自动切换行情订阅
watch(
  () => currentContract.value,
  async (newContract, oldContract) => {
    if (newContract && newContract.code !== oldContract?.code) {
      console.log('🔄 检测到合约变化:', {
        从: oldContract?.code || '无',
        到: newContract.code
      })
      
      // 更新面板合约信息
      panelContract.value = newContract
      
      // 重新订阅新合约的行情数据
      const subscribeSuccess = await queryMarketData(newContract.code)
      if (subscribeSuccess) {
        message.success(`已切换到 ${newContract.name} (${newContract.code}) 行情`)
      } else {
        message.warning(`切换到 ${newContract.name} 行情失败`)
      }
    }
  },
  { immediate: false } // 不立即执行，避免初始化时重复订阅
)
```

### 5. 组件卸载清理

```typescript
onUnmounted(async () => {
  console.log('🔄 交易面板开始卸载，清理资源...')
  
  document.removeEventListener('keydown', handleKeydown)

  // 清理定时器
  if (timeUpdateInterval) {
    clearInterval(timeUpdateInterval)
    timeUpdateInterval = null
    console.log('✅ 时间更新定时器已清理')
  }

  // 移除行情数据监听器
  if (marketDataListener) {
    ctpService.off('market_data', marketDataListener)
    marketDataListener = null
    console.log('✅ 行情数据监听器已移除')
  }

  // 取消订阅所有已订阅的合约行情数据
  if (subscribedContracts.value.length > 0) {
    try {
      console.log('🔄 取消订阅合约行情:', subscribedContracts.value)
      const result = await ctpService.unsubscribeMarketData(subscribedContracts.value)
      
      if (result.success) {
        console.log('✅ 成功取消订阅所有合约行情')
        message.success(`已取消订阅 ${subscribedContracts.value.join(', ')} 行情数据`)
      } else {
        console.warn('⚠️ 取消订阅部分失败:', result.error)
        message.warning(`取消订阅失败: ${result.error}`)
      }
    } catch (error) {
      console.error('❌ 取消订阅行情数据异常:', error)
      message.error(`取消订阅异常: ${error instanceof Error ? error.message : '未知错误'}`)
    }
    
    // 清空已订阅列表
    subscribedContracts.value = []
  }

  console.log('✅ 交易面板资源清理完成')
})
```

## 关键特性

1. **完整的资源清理**：确保所有监听器、订阅和定时器都被正确清理
2. **合约切换支持**：当用户切换合约时，自动取消旧订阅并建立新订阅
3. **错误处理**：包含完整的错误处理和用户反馈
4. **调试友好**：详细的控制台日志，便于调试和监控
5. **防重复订阅**：避免重复订阅同一合约
6. **异步安全**：正确处理异步操作，避免竞态条件

## 使用效果

- ✅ 关闭面板时自动断开行情数据连接
- ✅ 切换合约时自动更新行情订阅
- ✅ 防止内存泄漏和资源浪费
- ✅ 提供清晰的用户反馈
- ✅ 完整的错误处理机制
