impl :: bincode :: Encode for CThostFtdcReturnResultField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ReturnCode, encoder) ?; ::
        bincode :: Encode :: encode(&self.DescrInfoForReturnCode, encoder) ?;
        core :: result :: Result :: Ok(())
    }
}