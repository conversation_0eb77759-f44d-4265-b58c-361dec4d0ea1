impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBrokerTradingParamsField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, MarginPriceType : ::
            bincode :: Decode :: decode(decoder) ?, Algorithm : :: bincode ::
            Decode :: decode(decoder) ?, AvailIncludeCloseProfit : :: bincode
            :: Decode :: decode(decoder) ?, CurrencyID : :: bincode :: Decode
            :: decode(decoder) ?, OptionRoyaltyPriceType : :: bincode ::
            Decode :: decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBrokerTradingParamsField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, MarginPriceType :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Algorithm : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
            AvailIncludeCloseProfit : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, CurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OptionRoyaltyPriceType : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, AccountID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}