impl :: bincode :: Encode for CThostFtdcSyncDeltaProductExchRateField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ProductID, encoder) ?; :: bincode
        :: Encode :: encode(&self.QuoteCurrencyID, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeRate, encoder) ?; :: bincode :: Encode
        :: encode(&self.ActionDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.SyncDeltaSequenceNo, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}