impl :: bincode :: Encode for CThostFtdcRspTransferField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradeCode, encoder) ?; :: bincode
        :: Encode :: encode(&self.BankID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankBranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerBranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.PlateSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastFragment, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CustomerName, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdCardType, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdentifiedCardNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.CustType, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankAccount, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankPassWord, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Password, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.VerifyCertNoFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureFetchAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.FeePayFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.CustFee, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerFee, encoder) ?; :: bincode :: Encode ::
        encode(&self.Message, encoder) ?; :: bincode :: Encode ::
        encode(&self.Digest, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankAccType, encoder) ?; :: bincode :: Encode ::
        encode(&self.DeviceID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankSecuAccType, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerIDByBank, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankSecuAcc, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankPwdFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.SecuPwdFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.OperNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TransferStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorMsg, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongCustomerName, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}