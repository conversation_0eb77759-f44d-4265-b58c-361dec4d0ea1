impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDeltaInitInvstMarginField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?,
            LastRiskTotalInvstMargin : :: bincode :: Decode :: decode(decoder)
            ?, LastRiskTotalExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, ThisSyncInvstMargin : :: bincode :: Decode ::
            decode(decoder) ?, ThisSyncExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, RemainRiskInvstMargin : :: bincode :: Decode ::
            decode(decoder) ?, RemainRiskExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, LastRiskSpecTotalInvstMargin : :: bincode ::
            Decode :: decode(decoder) ?, LastRiskSpecTotalExchMargin : ::
            bincode :: Decode :: decode(decoder) ?, ThisSyncSpecInvstMargin :
            :: bincode :: Decode :: decode(decoder) ?, ThisSyncSpecExchMargin
            : :: bincode :: Decode :: decode(decoder) ?,
            RemainRiskSpecInvstMargin : :: bincode :: Decode ::
            decode(decoder) ?, RemainRiskSpecExchMargin : :: bincode :: Decode
            :: decode(decoder) ?, SyncDeltaSequenceNo : :: bincode :: Decode
            :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDeltaInitInvstMarginField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
            LastRiskTotalInvstMargin : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, LastRiskTotalExchMargin :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ThisSyncInvstMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ThisSyncExchMargin : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, RemainRiskInvstMargin : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            RemainRiskExchMargin : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?,
            LastRiskSpecTotalInvstMargin : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?,
            LastRiskSpecTotalExchMargin : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, ThisSyncSpecInvstMargin :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ThisSyncSpecExchMargin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            RemainRiskSpecInvstMargin : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, RemainRiskSpecExchMargin :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SyncDeltaSequenceNo : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}