impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInvestorField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InvestorID : :: bincode :: Decode :: decode(decoder) ?, BrokerID :
            :: bincode :: Decode :: decode(decoder) ?, InvestorGroupID : ::
            bincode :: Decode :: decode(decoder) ?, InvestorName : :: bincode
            :: Decode :: decode(decoder) ?, IdentifiedCardType : :: bincode ::
            Decode :: decode(decoder) ?, IdentifiedCardNo : :: bincode ::
            Decode :: decode(decoder) ?, IsActive : :: bincode :: Decode ::
            decode(decoder) ?, Telephone : :: bincode :: Decode ::
            decode(decoder) ?, Address : :: bincode :: Decode ::
            decode(decoder) ?, OpenDate : :: bincode :: Decode ::
            decode(decoder) ?, Mobile : :: bincode :: Decode ::
            decode(decoder) ?, CommModelID : :: bincode :: Decode ::
            decode(decoder) ?, MarginModelID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInvestorField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InvestorID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorGroupID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorName : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IdentifiedCardType : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, IdentifiedCardNo : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IsActive : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Telephone : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Address : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OpenDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Mobile : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CommModelID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, MarginModelID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}