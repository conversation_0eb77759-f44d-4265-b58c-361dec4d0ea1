impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInputQuoteField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, reserve1 : :: bincode
            :: Decode :: decode(decoder) ?, QuoteRef : :: bincode :: Decode ::
            decode(decoder) ?, UserID : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume : :: bincode :: Decode ::
            decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?, BusinessUnit : :: bincode :: Decode ::
            decode(decoder) ?, AskOffsetFlag : :: bincode :: Decode ::
            decode(decoder) ?, BidOffsetFlag : :: bincode :: Decode ::
            decode(decoder) ?, AskHedgeFlag : :: bincode :: Decode ::
            decode(decoder) ?, BidHedgeFlag : :: bincode :: Decode ::
            decode(decoder) ?, AskOrderRef : :: bincode :: Decode ::
            decode(decoder) ?, BidOrderRef : :: bincode :: Decode ::
            decode(decoder) ?, ForQuoteSysID : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?, ClientID : :: bincode :: Decode ::
            decode(decoder) ?, reserve2 : :: bincode :: Decode ::
            decode(decoder) ?, MacAddress : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?, IPAddress : :: bincode :: Decode ::
            decode(decoder) ?, ReplaceSysID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInputQuoteField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, reserve1 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, QuoteRef : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UserID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidPrice : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskVolume : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, RequestID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BusinessUnit : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskOffsetFlag : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BidOffsetFlag : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskHedgeFlag : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BidHedgeFlag : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskOrderRef : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidOrderRef : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ForQuoteSysID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestUnitID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ClientID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, MacAddress : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IPAddress : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ReplaceSysID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}