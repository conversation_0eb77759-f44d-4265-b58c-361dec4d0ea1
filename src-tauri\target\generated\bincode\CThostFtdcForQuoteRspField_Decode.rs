impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcForQuoteRspField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: Decode :: decode(decoder) ?, reserve1 :
            :: bincode :: Decode :: decode(decoder) ?, ForQuoteSysID : ::
            bincode :: Decode :: decode(decoder) ?, ForQuoteTime : :: bincode
            :: Decode :: decode(decoder) ?, ActionDay : :: bincode :: Decode
            :: decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcForQuoteRspField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve1 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ForQuoteSysID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ForQuoteTime : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ActionDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchangeID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstrumentID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}