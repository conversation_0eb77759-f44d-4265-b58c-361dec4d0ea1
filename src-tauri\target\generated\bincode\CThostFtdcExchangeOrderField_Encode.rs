impl :: bincode :: Encode for CThostFtdcExchangeOrderField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.OrderPriceType, encoder) ?; ::
        bincode :: Encode :: encode(&self.Direction, encoder) ?; :: bincode ::
        Encode :: encode(&self.CombOffsetFlag, encoder) ?; :: bincode ::
        Encode :: encode(&self.CombHedgeFlag, encoder) ?; :: bincode :: Encode
        :: encode(&self.LimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeTotalOriginal, encoder) ?; :: bincode :: Encode ::
        encode(&self.TimeCondition, encoder) ?; :: bincode :: Encode ::
        encode(&self.GTDDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeCondition, encoder) ?; :: bincode :: Encode ::
        encode(&self.MinVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.ContingentCondition, encoder) ?; :: bincode :: Encode ::
        encode(&self.StopPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForceCloseReason, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsAutoSuspend, encoder) ?; :: bincode :: Encode ::
        encode(&self.BusinessUnit, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ParticipantID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.TraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderSubmitStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.NotifySequence, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderSysID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderSource, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderType, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeTraded, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeTotal, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActiveTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.SuspendTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpdateTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.CancelTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActiveTraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClearingPartID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SequenceNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.BranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}