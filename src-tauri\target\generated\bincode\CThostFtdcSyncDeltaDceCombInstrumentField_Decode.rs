impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDeltaDceCombInstrumentField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            CombInstrumentID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeInstID : :: bincode :: Decode :: decode(decoder) ?,
            TradeGroupID : :: bincode :: Decode :: decode(decoder) ?,
            CombHedgeFlag : :: bincode :: Decode :: decode(decoder) ?,
            CombinationType : :: bincode :: Decode :: decode(decoder) ?,
            Direction : :: bincode :: Decode :: decode(decoder) ?, ProductID :
            :: bincode :: Decode :: decode(decoder) ?, Xparameter : :: bincode
            :: Decode :: decode(decoder) ?, ActionDirection : :: bincode ::
            Decode :: decode(decoder) ?, SyncDeltaSequenceNo : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDeltaDceCombInstrumentField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            CombInstrumentID : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ExchangeID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeInstID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeGroupID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CombHedgeFlag : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CombinationType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            Direction : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ProductID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Xparameter : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ActionDirection : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?,
        })
    }
}