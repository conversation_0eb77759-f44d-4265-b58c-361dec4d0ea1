impl :: bincode :: Encode for CThostFtdcSuperUserFunctionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.UserID, encoder) ?; :: bincode ::
        Encode :: encode(&self.FunctionCode, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}