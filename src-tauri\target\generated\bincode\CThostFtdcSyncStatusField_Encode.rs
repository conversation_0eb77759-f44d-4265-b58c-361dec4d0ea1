impl :: bincode :: Encode for CThostFtdcSyncStatusField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.DataSyncStatus, encoder) ?; core ::
        result :: Result :: Ok(())
    }
}