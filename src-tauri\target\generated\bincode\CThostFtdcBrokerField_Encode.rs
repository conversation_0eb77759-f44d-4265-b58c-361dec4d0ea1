impl :: bincode :: Encode for CThostFtdcBrokerField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.BrokerAbbr, encoder) ?; :: bincode :: Encode
        :: encode(&self.BrokerName, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsActive, encoder) ?; core :: result :: Result :: Ok(())
    }
}