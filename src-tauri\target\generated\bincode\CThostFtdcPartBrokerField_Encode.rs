impl :: bincode :: Encode for CThostFtdcPartBrokerField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode
        :: encode(&self.ParticipantID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsActive, encoder) ?; core :: result :: Result :: Ok(())
    }
}