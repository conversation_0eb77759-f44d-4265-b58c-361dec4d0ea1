impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcTransferSerialField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            PlateSerial : :: bincode :: Decode :: decode(decoder) ?, TradeDate
            : :: bincode :: Decode :: decode(decoder) ?, TradingDay : ::
            bincode :: Decode :: decode(decoder) ?, TradeTime : :: bincode ::
            Decode :: decode(decoder) ?, TradeCode : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, BankID : :: bincode :: Decode ::
            decode(decoder) ?, BankBranchID : :: bincode :: Decode ::
            decode(decoder) ?, BankAccType : :: bincode :: Decode ::
            decode(decoder) ?, BankAccount : :: bincode :: Decode ::
            decode(decoder) ?, BankSerial : :: bincode :: Decode ::
            decode(decoder) ?, BrokerID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerBranchID : :: bincode :: Decode ::
            decode(decoder) ?, FutureAccType : :: bincode :: Decode ::
            decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, InvestorID : :: bincode :: Decode ::
            decode(decoder) ?, FutureSerial : :: bincode :: Decode ::
            decode(decoder) ?, IdCardType : :: bincode :: Decode ::
            decode(decoder) ?, IdentifiedCardNo : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?, TradeAmount : :: bincode :: Decode ::
            decode(decoder) ?, CustFee : :: bincode :: Decode ::
            decode(decoder) ?, BrokerFee : :: bincode :: Decode ::
            decode(decoder) ?, AvailabilityFlag : :: bincode :: Decode ::
            decode(decoder) ?, OperatorCode : :: bincode :: Decode ::
            decode(decoder) ?, BankNewAccount : :: bincode :: Decode ::
            decode(decoder) ?, ErrorID : :: bincode :: Decode ::
            decode(decoder) ?, ErrorMsg : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcTransferSerialField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            PlateSerial : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradingDay : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeCode : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SessionID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BankID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankBranchID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BankAccType : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankAccount : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BankSerial : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerBranchID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FutureAccType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AccountID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, FutureSerial : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IdCardType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, IdentifiedCardNo :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CurrencyID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeAmount : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CustFee : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, BrokerFee : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AvailabilityFlag : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, OperatorCode : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BankNewAccount : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ErrorID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, ErrorMsg : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}