impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSettlementInfoField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: Decode :: decode(decoder) ?,
            SettlementID : :: bincode :: Decode :: decode(decoder) ?, BrokerID
            : :: bincode :: Decode :: decode(decoder) ?, InvestorID : ::
            bincode :: Decode :: decode(decoder) ?, SequenceNo : :: bincode ::
            Decode :: decode(decoder) ?, Content : :: bincode :: Decode ::
            decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSettlementInfoField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SettlementID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, SequenceNo : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Content : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, AccountID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CurrencyID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}