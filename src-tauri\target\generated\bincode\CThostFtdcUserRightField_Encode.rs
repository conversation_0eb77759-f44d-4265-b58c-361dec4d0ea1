impl :: bincode :: Encode for CThostFtdcUserRightField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserRightType, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsForbidden, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}