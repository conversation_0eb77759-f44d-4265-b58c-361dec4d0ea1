Running in development mode
cargo:rustc-link-search=ctp/v6.6.5_20210924/win_x64
cargo:rustc-link-search=native=ctp/v6.6.5_20210924/win_x64
cargo:rustc-link-lib=thostmduserapi_se
cargo:rustc-link-lib=thosttraderapi_se
cargo:rerun-if-changed=wrapper.hpp
cargo:rerun-if-env-changed=TAURI_CONFIG
cargo:rustc-check-cfg=cfg(desktop)
cargo:rustc-cfg=desktop
cargo:rustc-check-cfg=cfg(mobile)
cargo:rerun-if-changed=D:\demo\tauri-vue\src-tauri\tauri.conf.json
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_APP_NAME=app
cargo:rustc-env=TAURI_ANDROID_PACKAGE_NAME_PREFIX=com_tauri_1app_1vue
cargo:rustc-check-cfg=cfg(dev)
cargo:rustc-cfg=dev
cargo:PERMISSION_FILES_PATH=D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out\app-manifest\__app__-permission-files
cargo:rerun-if-changed=capabilities
cargo:rerun-if-env-changed=REMOVE_UNUSED_COMMANDS
cargo:rustc-env=TAURI_ENV_TARGET_TRIPLE=x86_64-pc-windows-msvc
package.metadata does not exist
Microsoft (R) Windows (R) Resource Compiler Version 10.0.10011.16384

Copyright (C) Microsoft Corporation.  All rights reserved.


cargo:rustc-link-arg-bins=D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out\resource.lib
