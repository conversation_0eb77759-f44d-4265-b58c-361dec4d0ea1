D:\demo\tauri-vue\src-tauri\target\debug\deps\tauri_app_vue_lib.d: src\lib.rs src\ffi_utils.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\demo\tauri-vue\src-tauri\target\debug\deps\tauri_app_vue_lib.lib: src\lib.rs src\ffi_utils.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\demo\tauri-vue\src-tauri\target\debug\deps\tauri_app_vue_lib.dll: src\lib.rs src\ffi_utils.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out/bindings.rs src\md_impl.rs src\trade_impl.rs

D:\demo\tauri-vue\src-tauri\target\debug\deps\libtauri_app_vue_lib.rlib: src\lib.rs src\ffi_utils.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out/bindings.rs src\md_impl.rs src\trade_impl.rs

src\lib.rs:
src\ffi_utils.rs:
D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-92b75f8d9c7a8e91\out/bindings.rs:
src\md_impl.rs:
src\trade_impl.rs:

# env-dep:OUT_DIR=D:\\demo\\tauri-vue\\src-tauri\\target\\debug\\build\\tauri_app_vue-92b75f8d9c7a8e91\\out
