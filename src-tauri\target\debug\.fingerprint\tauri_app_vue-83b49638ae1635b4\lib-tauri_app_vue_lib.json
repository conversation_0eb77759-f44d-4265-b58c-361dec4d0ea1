{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 5442004388195113429, "profile": 8731458305071235362, "path": 10763286916239946207, "deps": [[1556188385098558368, "simple_error", false, 13617186872880817637], [2706460456408817945, "futures", false, 13817049086707830965], [5746673292440776345, "env_logger", false, 16892472386286749392], [8606274917505247608, "tracing", false, 3286878897894873495], [9689903380558560274, "serde", false, 7595997975396347136], [9897246384292347999, "chrono", false, 16586790275969388669], [11862723254873017773, "bincode", false, 16520771757198954277], [12393800526703971956, "tokio", false, 9539897604659612486], [13625485746686963219, "anyhow", false, 10602976733193444474], [14039947826026167952, "tauri", false, 13711311194167194262], [15180317924524316486, "build_script_build", false, 5412691884573332249], [15367738274754116744, "serde_json", false, 8279389256016779385], [15932120279885307830, "memchr", false, 17647054705086699841], [16230660778393187092, "tracing_subscriber", false, 9189319579615748553], [16257276029081467297, "serde_derive", false, 13354419331583024027], [16702348383442838006, "tauri_plugin_opener", false, 11361159047576606158], [17675327481376616781, "encoding", false, 1016135538577592845], [17874132307072864906, "time", false, 5452241424436106921], [17917672826516349275, "lazy_static", false, 1410547945167897730]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri_app_vue-83b49638ae1635b4\\dep-lib-tauri_app_vue_lib", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}