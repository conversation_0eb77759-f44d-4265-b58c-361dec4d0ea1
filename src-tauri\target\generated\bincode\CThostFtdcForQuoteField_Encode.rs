impl :: bincode :: Encode for CThostFtdcForQuoteField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForQuoteRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForQuoteLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ParticipantID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.TraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForQuoteStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrontID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.StatusMsg, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActiveUserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerForQutoSeq, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve3, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}