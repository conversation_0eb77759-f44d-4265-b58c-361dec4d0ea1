impl :: bincode :: Encode for CThostFtdcTransferBankField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BankID, encoder) ?; :: bincode ::
        Encode :: encode(&self.BankBrchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankName, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsActive, encoder) ?; core :: result :: Result :: Ok(())
    }
}