impl :: bincode :: Encode for CThostFtdcCFMMCTradingAccountTokenField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ParticipantID, encoder) ?; :: bincode ::
        Encode :: encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.KeyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Token, encoder) ?; core :: result :: Result :: Ok(())
    }
}