impl < __Context > :: bincode :: Decode < __Context > for CThostFtdcOrderField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, reserve1 : :: bincode
            :: Decode :: decode(decoder) ?, OrderRef : :: bincode :: Decode ::
            decode(decoder) ?, UserID : :: bincode :: Decode ::
            decode(decoder) ?, OrderPriceType : :: bincode :: Decode ::
            decode(decoder) ?, Direction : :: bincode :: Decode ::
            decode(decoder) ?, CombOffsetFlag : :: bincode :: Decode ::
            decode(decoder) ?, CombHedgeFlag : :: bincode :: Decode ::
            decode(decoder) ?, LimitPrice : :: bincode :: Decode ::
            decode(decoder) ?, VolumeTotalOriginal : :: bincode :: Decode ::
            decode(decoder) ?, TimeCondition : :: bincode :: Decode ::
            decode(decoder) ?, GTDDate : :: bincode :: Decode ::
            decode(decoder) ?, VolumeCondition : :: bincode :: Decode ::
            decode(decoder) ?, MinVolume : :: bincode :: Decode ::
            decode(decoder) ?, ContingentCondition : :: bincode :: Decode ::
            decode(decoder) ?, StopPrice : :: bincode :: Decode ::
            decode(decoder) ?, ForceCloseReason : :: bincode :: Decode ::
            decode(decoder) ?, IsAutoSuspend : :: bincode :: Decode ::
            decode(decoder) ?, BusinessUnit : :: bincode :: Decode ::
            decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?, OrderLocalID : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, ParticipantID : :: bincode :: Decode ::
            decode(decoder) ?, ClientID : :: bincode :: Decode ::
            decode(decoder) ?, reserve2 : :: bincode :: Decode ::
            decode(decoder) ?, TraderID : :: bincode :: Decode ::
            decode(decoder) ?, InstallID : :: bincode :: Decode ::
            decode(decoder) ?, OrderSubmitStatus : :: bincode :: Decode ::
            decode(decoder) ?, NotifySequence : :: bincode :: Decode ::
            decode(decoder) ?, TradingDay : :: bincode :: Decode ::
            decode(decoder) ?, SettlementID : :: bincode :: Decode ::
            decode(decoder) ?, OrderSysID : :: bincode :: Decode ::
            decode(decoder) ?, OrderSource : :: bincode :: Decode ::
            decode(decoder) ?, OrderStatus : :: bincode :: Decode ::
            decode(decoder) ?, OrderType : :: bincode :: Decode ::
            decode(decoder) ?, VolumeTraded : :: bincode :: Decode ::
            decode(decoder) ?, VolumeTotal : :: bincode :: Decode ::
            decode(decoder) ?, InsertDate : :: bincode :: Decode ::
            decode(decoder) ?, InsertTime : :: bincode :: Decode ::
            decode(decoder) ?, ActiveTime : :: bincode :: Decode ::
            decode(decoder) ?, SuspendTime : :: bincode :: Decode ::
            decode(decoder) ?, UpdateTime : :: bincode :: Decode ::
            decode(decoder) ?, CancelTime : :: bincode :: Decode ::
            decode(decoder) ?, ActiveTraderID : :: bincode :: Decode ::
            decode(decoder) ?, ClearingPartID : :: bincode :: Decode ::
            decode(decoder) ?, SequenceNo : :: bincode :: Decode ::
            decode(decoder) ?, FrontID : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, UserProductInfo : :: bincode :: Decode ::
            decode(decoder) ?, StatusMsg : :: bincode :: Decode ::
            decode(decoder) ?, UserForceClose : :: bincode :: Decode ::
            decode(decoder) ?, ActiveUserID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerOrderSeq : :: bincode :: Decode ::
            decode(decoder) ?, RelativeOrderSysID : :: bincode :: Decode ::
            decode(decoder) ?, ZCETotalTradedVolume : :: bincode :: Decode ::
            decode(decoder) ?, IsSwapOrder : :: bincode :: Decode ::
            decode(decoder) ?, BranchID : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?, reserve3 : :: bincode :: Decode ::
            decode(decoder) ?, MacAddress : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeInstID : :: bincode :: Decode ::
            decode(decoder) ?, IPAddress : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcOrderField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, reserve1 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderRef : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UserID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderPriceType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            Direction : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CombOffsetFlag : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CombHedgeFlag : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LimitPrice : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
            VolumeTotalOriginal : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, TimeCondition : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            GTDDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, VolumeCondition : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MinVolume : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ContingentCondition : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            StopPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ForceCloseReason : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IsAutoSuspend : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BusinessUnit : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            RequestID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderLocalID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ParticipantID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ClientID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TraderID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstallID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OrderSubmitStatus
            : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, NotifySequence : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SettlementID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OrderSysID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderSource : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OrderStatus : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, VolumeTraded : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, VolumeTotal : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InsertDate : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InsertTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ActiveTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SuspendTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UpdateTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CancelTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ActiveTraderID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClearingPartID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SequenceNo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrontID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, SessionID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            UserProductInfo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, StatusMsg : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UserForceClose :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ActiveUserID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BrokerOrderSeq : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, RelativeOrderSysID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ZCETotalTradedVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, IsSwapOrder : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BranchID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestUnitID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AccountID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CurrencyID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, reserve3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, MacAddress : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstrumentID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchangeInstID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IPAddress : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}