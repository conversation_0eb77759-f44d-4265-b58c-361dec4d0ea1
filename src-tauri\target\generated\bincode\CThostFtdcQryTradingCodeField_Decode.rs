impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcQryTradingCodeField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, ExchangeID : :: bincode
            :: Decode :: decode(decoder) ?, ClientID : :: bincode :: Decode ::
            decode(decoder) ?, ClientIDType : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcQryTradingCodeField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClientID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ClientIDType : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestUnitID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}