impl :: bincode :: Encode for CThostFtdcSettlementInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.SettlementID, encoder) ?; :: bincode
        :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode :: Encode
        :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SequenceNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.Content, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}