# 交易面板功能说明

## 概述
这是一个基于 Vue 3 + TypeScript 开发的期货交易面板界面，模拟了专业交易软件的核心功能。

## 主要功能

### 1. 合约信息显示功能
- **+号-号功能**: 单个合约字体、格子放大缩小变化功能
- **时间功能**: 精准时间显示功能
- **实时深度缩略**: 实时市场深度数据
- **成交量**: 当前成交量显示
- **持仓量**: 当前持仓量显示
- **日增减仓**: 日增减仓数据显示

### 2. A模式、B模式功能
- **A模式**: 不同模式下不同单状态（详细见视频解说），后期制作需与撤单限制功能对换位置
- **B模式**: 默认A模式状态，默认B模式状态

### 3. 撤单限制功能
- 点选第一个为最大撤489手，点选第二个为不限制撤单数量
- 后期制作需A、B模式选择功能对换位置，默认A模式状态

### 4. 平仓开仓模式
- 勾选后为仅开仓模式，取消后为仅平仓模式
- 单个合约独立更改，非统一更改模式，默认关闭状态

### 5. 当前撤单总数显示
- 实时显示当前撤单总数

### 6. 持仓手数显示
- 多单为正数，空单为负数

### 7. P为当前合约平仓模式，K为当前合约开仓模式

### 8. 颜色标识说明
- **红色为空单**（进单后为粉红色）
- **蓝色为多单**（进单后为浅蓝色）
- **中间浅灰色**为未有人出价的价格位置

### 9. 深灰色格子为上个交易日收盘价

### 10. 黄色为当前最高价，绿线为当前最低价，上下紫色线为涨停板价

## 快捷键说明

| 快捷键 | 功能 |
|--------|------|
| + / = | 放大字体和格子 |
| - | 缩小字体和格子 |
| Enter | 下单 |
| Delete / Backspace | 撤单 |
| Esc | 清除选择 |
| P | 切换开仓/平仓模式 |
| K | 当前合约开仓模式 |

## 使用方法

1. **选择价格/数量**: 点击左侧价格表格中的价格或数量
2. **设置下单参数**: 在右侧控制面板中设置数量、价格、模式等
3. **执行操作**: 使用按钮或快捷键执行下单、撤单等操作
4. **查看帮助**: 点击右侧"功能说明"查看详细帮助信息
5. **使用快捷键**: 利用键盘快捷键提高操作效率

## 技术特性

- **响应式设计**: 支持界面缩放功能
- **实时数据**: 模拟实时行情数据更新
- **键盘快捷键**: 支持专业交易员常用的快捷键操作
- **状态管理**: 完整的交易状态和模式管理
- **用户体验**: 符合专业交易软件的操作习惯

## 开发说明

- 基于 Vue 3 Composition API
- 使用 TypeScript 提供类型安全
- 响应式数据管理
- 模块化组件设计
- 可扩展的架构设计

## 注意事项

- 这是一个演示界面，不连接真实的交易系统
- 所有数据都是模拟数据
- 实际使用时需要集成真实的交易API
- 建议在专业指导下进行实际交易操作
