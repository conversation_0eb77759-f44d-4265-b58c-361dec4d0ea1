impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcExchangeMarginRateAdjustField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, reserve1 :
            :: bincode :: Decode :: decode(decoder) ?, HedgeFlag : :: bincode
            :: Decode :: decode(decoder) ?, LongMarginRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?, LongMarginRatioByVolume :
            :: bincode :: Decode :: decode(decoder) ?, ShortMarginRatioByMoney
            : :: bincode :: Decode :: decode(decoder) ?,
            ShortMarginRatioByVolume : :: bincode :: Decode :: decode(decoder)
            ?, ExchLongMarginRatioByMoney : :: bincode :: Decode ::
            decode(decoder) ?, ExchLongMarginRatioByVolume : :: bincode ::
            Decode :: decode(decoder) ?, ExchShortMarginRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?,
            ExchShortMarginRatioByVolume : :: bincode :: Decode ::
            decode(decoder) ?, NoLongMarginRatioByMoney : :: bincode :: Decode
            :: decode(decoder) ?, NoLongMarginRatioByVolume : :: bincode ::
            Decode :: decode(decoder) ?, NoShortMarginRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?, NoShortMarginRatioByVolume
            : :: bincode :: Decode :: decode(decoder) ?, InstrumentID : ::
            bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcExchangeMarginRateAdjustField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve1 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, HedgeFlag : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LongMarginRatioByMoney : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LongMarginRatioByVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, ShortMarginRatioByMoney :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ShortMarginRatioByVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchLongMarginRatioByMoney : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?,
            ExchLongMarginRatioByVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?,
            ExchShortMarginRatioByMoney : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?,
            ExchShortMarginRatioByVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, NoLongMarginRatioByMoney :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, NoLongMarginRatioByVolume : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            NoShortMarginRatioByMoney : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, NoShortMarginRatioByVolume
            : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}