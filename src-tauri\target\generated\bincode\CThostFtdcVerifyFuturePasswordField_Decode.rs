impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcVerifyFuturePasswordField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeCode : :: bincode :: Decode :: decode(decoder) ?, BankID : ::
            bincode :: Decode :: decode(decoder) ?, BankBranchID : :: bincode
            :: Decode :: decode(decoder) ?, BrokerID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerBranchID : :: bincode :: Decode ::
            decode(decoder) ?, TradeDate : :: bincode :: Decode ::
            decode(decoder) ?, TradeTime : :: bincode :: Decode ::
            decode(decoder) ?, BankSerial : :: bincode :: Decode ::
            decode(decoder) ?, TradingDay : :: bincode :: Decode ::
            decode(decoder) ?, PlateSerial : :: bincode :: Decode ::
            decode(decoder) ?, LastFragment : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, Password : :: bincode :: Decode ::
            decode(decoder) ?, BankAccount : :: bincode :: Decode ::
            decode(decoder) ?, BankPassWord : :: bincode :: Decode ::
            decode(decoder) ?, InstallID : :: bincode :: Decode ::
            decode(decoder) ?, TID : :: bincode :: Decode :: decode(decoder)
            ?, CurrencyID : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcVerifyFuturePasswordField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeCode : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, BankBranchID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerBranchID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankSerial : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradingDay : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PlateSerial : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, LastFragment : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SessionID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AccountID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Password : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BankAccount : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankPassWord : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InstallID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TID : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, CurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}