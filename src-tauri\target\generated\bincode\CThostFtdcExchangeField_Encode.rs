impl :: bincode :: Encode for CThostFtdcExchangeField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ExchangeID, encoder) ?; ::
        bincode :: Encode :: encode(&self.ExchangeName, encoder) ?; :: bincode
        :: Encode :: encode(&self.ExchangeProperty, encoder) ?; core :: result
        :: Result :: Ok(())
    }
}