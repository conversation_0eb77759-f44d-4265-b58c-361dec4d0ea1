impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInputOrderActionField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, OrderActionRef : ::
            bincode :: Decode :: decode(decoder) ?, OrderRef : :: bincode ::
            Decode :: decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?, FrontID : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, OrderSysID : :: bincode :: Decode ::
            decode(decoder) ?, ActionFlag : :: bincode :: Decode ::
            decode(decoder) ?, LimitPrice : :: bincode :: Decode ::
            decode(decoder) ?, VolumeChange : :: bincode :: Decode ::
            decode(decoder) ?, UserID : :: bincode :: Decode ::
            decode(decoder) ?, reserve1 : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?, reserve2 : :: bincode :: Decode ::
            decode(decoder) ?, MacAddress : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?, IPAddress : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInputOrderActionField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OrderActionRef :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderRef : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, RequestID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrontID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, SessionID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderSysID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ActionFlag : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LimitPrice : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, VolumeChange : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, reserve1 : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InvestUnitID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, MacAddress : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IPAddress : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}