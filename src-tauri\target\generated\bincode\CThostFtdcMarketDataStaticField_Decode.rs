impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcMarketDataStaticField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            OpenPrice : :: bincode :: Decode :: decode(decoder) ?,
            HighestPrice : :: bincode :: Decode :: decode(decoder) ?,
            LowestPrice : :: bincode :: Decode :: decode(decoder) ?,
            ClosePrice : :: bincode :: Decode :: decode(decoder) ?,
            UpperLimitPrice : :: bincode :: Decode :: decode(decoder) ?,
            LowerLimitPrice : :: bincode :: Decode :: decode(decoder) ?,
            SettlementPrice : :: bincode :: Decode :: decode(decoder) ?,
            CurrDelta : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcMarketDataStaticField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            OpenPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, HighestPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LowestPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClosePrice : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UpperLimitPrice :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LowerLimitPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SettlementPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CurrDelta : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}