impl :: bincode :: Encode for CThostFtdcVerifyCustInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.CustomerName, encoder) ?; ::
        bincode :: Encode :: encode(&self.IdCardType, encoder) ?; :: bincode
        :: Encode :: encode(&self.IdentifiedCardNo, encoder) ?; :: bincode ::
        Encode :: encode(&self.CustType, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongCustomerName, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}