{"rustc": 1842507548689473721, "features": "[]", "declared_features": "[]", "target": 14065169983031974225, "profile": 17672942494452627365, "path": 9686553099741370331, "deps": [[1556188385098558368, "simple_error", false, 4846649623194504001], [2706460456408817945, "futures", false, 6494457859858392251], [5746673292440776345, "env_logger", false, 9899537615790415121], [8606274917505247608, "tracing", false, 18361276229073147347], [9689903380558560274, "serde", false, 1637510295612489352], [9897246384292347999, "chrono", false, 95242817413668523], [11862723254873017773, "bincode", false, 9693995769085223824], [12393800526703971956, "tokio", false, 9437389424145820357], [13625485746686963219, "anyhow", false, 1676312903101786736], [14039947826026167952, "tauri", false, 10145308084144802239], [15180317924524316486, "tauri_app_vue_lib", false, 11897104573552068191], [15180317924524316486, "build_script_build", false, 4128538379469752552], [15367738274754116744, "serde_json", false, 8504368567621843844], [15932120279885307830, "memchr", false, 5820369818303770929], [16230660778393187092, "tracing_subscriber", false, 10611121118916585668], [16257276029081467297, "serde_derive", false, 13354419331583024027], [16702348383442838006, "tauri_plugin_opener", false, 6539677397653042723], [17675327481376616781, "encoding", false, 13021048955884168783], [17874132307072864906, "time", false, 341247637291112465], [17917672826516349275, "lazy_static", false, 9095722739167758011]], "local": [{"CheckDepInfo": {"dep_info": "debug\\.fingerprint\\tauri_app_vue-6046ce1da2a1a2cb\\dep-example-md_api", "checksum": false}}], "rustflags": [], "config": 2069994364910194474, "compile_kind": 0}