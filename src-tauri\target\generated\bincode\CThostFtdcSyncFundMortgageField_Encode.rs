impl :: bincode :: Encode for CThostFtdcSyncFundMortgageField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.MortgageSeqNo, encoder) ?; ::
        bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode ::
        Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FromCurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.MortgageAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.ToCurrencyID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}