# TradingPanel 字段说明指南

## 概述

本文档详细说明了TradingPanel中所有字段的含义和用途，帮助用户更好地理解交易界面。

## 市场数据字段

### 价格相关
- **priceChangePercent**: 价格变化百分比（相对于昨结算价）
  - 显示当前价格相对于昨日结算价的涨跌幅
  - 正值表示上涨，负值表示下跌

### 成交量和持仓量
- **totalVolume**: 总成交量（手）
  - 当日累计成交的合约数量
  - 反映市场活跃度

- **totalPosition**: 总持仓量（手）
  - 市场上未平仓的合约总数
  - 也称为未平仓合约数或持仓兴趣
  - 反映市场参与者的总体持仓规模

- **dailyPositionChange**: 日内持仓变化（手）
  - 相对于昨日的持仓量变化
  - 正值表示持仓增加，负值表示持仓减少

## 交易控制字段

### 下单参数
- **orderQuantity**: 下单数量（手）
  - 用户输入的下单手数
  - 默认值为1手

- **orderPrice**: 下单价格
  - 用户输入的下单价格
  - 点击价格档位时会自动填入该价格

- **orderType**: 订单类型
  - A: 默认模式
  - B: 特殊模式（具体功能待定义）

### 交易模式
- **cancelMode**: 撤单模式
  - 'limited': 限制撤单次数
  - 'unlimited': 无限制撤单

- **positionMode**: 持仓模式
  - 'open': 仅开仓模式
  - 'close': 仅平仓模式

- **maxCancelOrders**: 最大撤单次数限制
  - 在限制模式下的最大撤单次数

- **currentCancelCount**: 当前已撤单次数
  - 已经执行的撤单次数计数

## 持仓信息字段

### 持仓数据
- **netPosition**: 净持仓
  - 多头持仓 - 空头持仓的净值
  - 正值表示净多头，负值表示净空头

- **cPosition**: C仓位
  - 可能指Close平仓相关持仓
  - 具体含义需要根据交易系统定义

- **tPosition**: T仓位
  - 可能指Today今日持仓
  - 区别于昨日持仓

- **pnlValue**: 盈亏值
  - Profit and Loss，持仓盈亏
  - 反映当前持仓的浮动盈亏

### 账户资金（真实数据模式）
- **accountInfo.available**: 可用资金
  - 可用于开新仓的资金
  - 扣除保证金占用后的余额

- **accountInfo.balance**: 账户余额
  - 账户总资金
  - 包括可用资金和保证金占用

## 交易选项字段

### 自动化选项
- **options.autoHand**: 自动手数
  - 是否自动计算下单手数
  - 根据资金和风险自动确定下单量

### 限制选项
- **options.cLimit345**: C限制345
  - 特定的交易限制规则
  - 具体规则需要根据交易策略定义

- **options.cLimit550**: C限制550
  - 另一种交易限制规则
  - 可能与价格或数量限制相关

- **options.noLimit**: 无限制
  - 取消所有交易限制
  - 允许不受限制的交易

- **options.noCombo**: 无组合
  - 禁用组合交易功能
  - 只允许单一合约交易

- **options.upLimit**: 涨停限制
  - 涨停价格限制开关
  - 防止在涨停价附近下单

## 界面控制字段

### 显示控制
- **fontSize**: 字体大小（像素）
  - 控制界面文字大小
  - 用于界面缩放

- **cellHeight**: 单元格高度（像素）
  - 控制价格档位的行高
  - 影响界面紧凑度

### 状态显示
- **currentTime**: 当前时间显示
  - 显示系统当前时间
  - 用于时间参考

- **redValue/blueValue**: 特殊标记值
  - 用于特殊情况的数值显示
  - 可能用于警告或提示

## CTP连接状态字段

### 连接状态
- **isCtpConnected**: CTP连接状态
  - true: 已连接到CTP服务器
  - false: 未连接

- **isUsingRealData**: 数据源类型
  - true: 使用真实CTP数据
  - false: 使用模拟数据

### API状态
- **mdStatus**: 行情API状态
  - 'login_success': 行情登录成功
  - 'connecting': 连接中
  - 'disconnected': 未连接

- **traderStatus**: 交易API状态
  - 'login_success': 交易登录成功
  - 'connecting': 连接中
  - 'disconnected': 未连接

## 使用建议

### 关键指标监控
1. **总持仓量变化**: 关注市场整体参与度
2. **成交量**: 判断市场活跃程度
3. **净持仓**: 了解自己的风险敞口
4. **可用资金**: 控制交易风险

### 交易操作
1. **下单前检查**: 确认数量、价格和模式设置
2. **撤单限制**: 注意撤单次数限制
3. **持仓模式**: 根据策略选择开仓或平仓模式

### 风险控制
1. **资金管理**: 监控可用资金和账户余额
2. **持仓监控**: 关注净持仓和盈亏情况
3. **连接状态**: 确保CTP连接稳定

## 故障排除

### 数据异常
- 检查CTP连接状态
- 确认是否使用真实数据
- 查看控制台日志

### 交易问题
- 检查交易API登录状态
- 确认账户资金充足
- 验证交易参数设置

---

**注意**: 部分字段的具体含义可能因交易系统和策略而异，请根据实际使用情况进行调整。
