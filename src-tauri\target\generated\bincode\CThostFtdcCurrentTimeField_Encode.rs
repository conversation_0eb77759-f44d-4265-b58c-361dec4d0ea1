impl :: bincode :: Encode for CThostFtdcCurrentTimeField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.CurrDate, encoder) ?; :: bincode
        :: Encode :: encode(&self.CurrTime, encoder) ?; :: bincode :: Encode
        :: encode(&self.CurrMillisec, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDay, encoder) ?; core :: result :: Result :: Ok(())
    }
}