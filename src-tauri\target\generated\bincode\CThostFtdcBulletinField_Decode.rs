impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBulletinField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: Decode :: decode(decoder) ?, TradingDay
            : :: bincode :: Decode :: decode(decoder) ?, BulletinID : ::
            bincode :: Decode :: decode(decoder) ?, SequenceNo : :: bincode ::
            Decode :: decode(decoder) ?, NewsType : :: bincode :: Decode ::
            decode(decoder) ?, NewsUrgency : :: bincode :: Decode ::
            decode(decoder) ?, SendTime : :: bincode :: Decode ::
            decode(decoder) ?, Abstract : :: bincode :: Decode ::
            decode(decoder) ?, ComeFrom : :: bincode :: Decode ::
            decode(decoder) ?, Content : :: bincode :: Decode ::
            decode(decoder) ?, URLLink : :: bincode :: Decode ::
            decode(decoder) ?, MarketID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBulletinField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradingDay : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BulletinID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SequenceNo : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, NewsType : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, NewsUrgency : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, SendTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Abstract : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ComeFrom : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Content : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, URLLink : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MarketID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}