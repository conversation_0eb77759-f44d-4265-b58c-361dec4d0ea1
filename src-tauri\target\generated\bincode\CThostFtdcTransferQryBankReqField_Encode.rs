impl :: bincode :: Encode for CThostFtdcTransferQryBankReqField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.FutureAccount, encoder) ?; ::
        bincode :: Encode :: encode(&self.FuturePwdFlag, encoder) ?; ::
        bincode :: Encode :: encode(&self.FutureAccPwd, encoder) ?; :: bincode
        :: Encode :: encode(&self.CurrencyCode, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}