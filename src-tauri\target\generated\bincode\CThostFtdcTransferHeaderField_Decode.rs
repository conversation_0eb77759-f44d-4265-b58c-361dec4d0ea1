impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcTransferHeaderField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            Version : :: bincode :: Decode :: decode(decoder) ?, TradeCode :
            :: bincode :: Decode :: decode(decoder) ?, TradeDate : :: bincode
            :: Decode :: decode(decoder) ?, TradeTime : :: bincode :: Decode
            :: decode(decoder) ?, TradeSerial : :: bincode :: Decode ::
            decode(decoder) ?, FutureID : :: bincode :: Decode ::
            decode(decoder) ?, BankID : :: bincode :: Decode ::
            decode(decoder) ?, BankBrchID : :: bincode :: Decode ::
            decode(decoder) ?, OperNo : :: bincode :: Decode ::
            decode(decoder) ?, DeviceID : :: bincode :: Decode ::
            decode(decoder) ?, RecordNum : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcTransferHeaderField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            Version : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeCode : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeDate : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeSerial : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FutureID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BankID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankBrchID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OperNo : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, DeviceID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, RecordNum : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SessionID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, RequestID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}