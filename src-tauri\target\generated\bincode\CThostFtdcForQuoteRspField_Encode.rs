impl :: bincode :: Encode for CThostFtdcForQuoteRspField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode ::
        Encode :: encode(&self.ForQuoteSysID, encoder) ?; :: bincode :: Encode
        :: encode(&self.ForQuoteTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}