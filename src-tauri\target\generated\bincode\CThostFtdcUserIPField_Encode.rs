impl :: bincode :: Encode for CThostFtdcUserIPField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.Broker<PERSON>, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPMask, encoder) ?; core :: result :: Result :: Ok(())
    }
}