impl :: bincode :: Encode for CThostFtdcSettlementInfoConfirmField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.ConfirmDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.ConfirmTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}