impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBrokerUserOTPParamField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, UserID : ::
            bincode :: Decode :: decode(decoder) ?, OTPVendorsID : :: bincode
            :: Decode :: decode(decoder) ?, SerialNumber : :: bincode ::
            Decode :: decode(decoder) ?, AuthKey : :: bincode :: Decode ::
            decode(decoder) ?, LastDrift : :: bincode :: Decode ::
            decode(decoder) ?, LastSuccess : :: bincode :: Decode ::
            decode(decoder) ?, OTPType : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBrokerUserOTPParamField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, OTPVendorsID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SerialNumber : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AuthKey : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LastDrift : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, LastSuccess : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OTPType : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}