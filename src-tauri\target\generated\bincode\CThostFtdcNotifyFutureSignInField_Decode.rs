impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcNotifyFutureSignInField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeCode : :: bincode :: Decode :: decode(decoder) ?, BankID : ::
            bincode :: Decode :: decode(decoder) ?, BankBranchID : :: bincode
            :: Decode :: decode(decoder) ?, BrokerID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerBranchID : :: bincode :: Decode ::
            decode(decoder) ?, TradeDate : :: bincode :: Decode ::
            decode(decoder) ?, TradeTime : :: bincode :: Decode ::
            decode(decoder) ?, BankSerial : :: bincode :: Decode ::
            decode(decoder) ?, TradingDay : :: bincode :: Decode ::
            decode(decoder) ?, PlateSerial : :: bincode :: Decode ::
            decode(decoder) ?, LastFragment : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, InstallID : :: bincode :: Decode ::
            decode(decoder) ?, UserID : :: bincode :: Decode ::
            decode(decoder) ?, Digest : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?, DeviceID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerIDByBank : :: bincode :: Decode ::
            decode(decoder) ?, OperNo : :: bincode :: Decode ::
            decode(decoder) ?, RequestID : :: bincode :: Decode ::
            decode(decoder) ?, TID : :: bincode :: Decode :: decode(decoder)
            ?, ErrorID : :: bincode :: Decode :: decode(decoder) ?, ErrorMsg :
            :: bincode :: Decode :: decode(decoder) ?, PinKey : :: bincode ::
            Decode :: decode(decoder) ?, MacKey : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcNotifyFutureSignInField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeCode : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, BankBranchID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerBranchID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankSerial : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradingDay : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PlateSerial : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, LastFragment : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SessionID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstallID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, Digest : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CurrencyID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, DeviceID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerIDByBank :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OperNo : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, RequestID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ErrorID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, ErrorMsg : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PinKey : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, MacKey : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}