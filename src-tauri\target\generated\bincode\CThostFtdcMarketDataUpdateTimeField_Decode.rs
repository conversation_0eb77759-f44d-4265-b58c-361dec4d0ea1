impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcMarketDataUpdateTimeField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?, UpdateTime :
            :: bincode :: Decode :: decode(decoder) ?, UpdateMillisec : ::
            bincode :: Decode :: decode(decoder) ?, ActionDay : :: bincode ::
            Decode :: decode(decoder) ?, InstrumentID : :: bincode :: Decode
            :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcMarketDataUpdateTimeField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UpdateTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UpdateMillisec :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ActionDay : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstrumentID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}