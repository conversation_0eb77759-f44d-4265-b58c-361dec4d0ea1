impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcAccountregisterField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeDay : :: bincode :: Decode :: decode(decoder) ?, BankID : ::
            bincode :: Decode :: decode(decoder) ?, BankBranchID : :: bincode
            :: Decode :: decode(decoder) ?, BankAccount : :: bincode :: Decode
            :: decode(decoder) ?, BrokerID : :: bincode :: Decode ::
            decode(decoder) ?, BrokerBranchID : :: bincode :: Decode ::
            decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, IdCardType : :: bincode :: Decode ::
            decode(decoder) ?, IdentifiedCardNo : :: bincode :: Decode ::
            decode(decoder) ?, CustomerName : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?, OpenOrDestroy : :: bincode :: Decode ::
            decode(decoder) ?, RegDate : :: bincode :: Decode ::
            decode(decoder) ?, OutDate : :: bincode :: Decode ::
            decode(decoder) ?, TID : :: bincode :: Decode :: decode(decoder)
            ?, CustType : :: bincode :: Decode :: decode(decoder) ?,
            BankAccType : :: bincode :: Decode :: decode(decoder) ?,
            LongCustomerName : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcAccountregisterField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradeDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, BankBranchID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankAccount : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerBranchID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AccountID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IdCardType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, IdentifiedCardNo :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CustomerName : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CurrencyID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OpenOrDestroy : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            RegDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OutDate : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, TID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CustType : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankAccType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, LongCustomerName :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}