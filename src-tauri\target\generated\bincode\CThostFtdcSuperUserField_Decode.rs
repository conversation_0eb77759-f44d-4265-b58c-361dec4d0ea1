impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSuperUserField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            UserID : :: bincode :: Decode :: decode(decoder) ?, UserName : ::
            bincode :: Decode :: decode(decoder) ?, Password : :: bincode ::
            Decode :: decode(decoder) ?, IsActive : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSuperUserField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            UserID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserName : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Password : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IsActive : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}