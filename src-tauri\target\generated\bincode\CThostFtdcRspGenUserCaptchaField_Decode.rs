impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcRspGenUserCaptchaField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, UserID : ::
            bincode :: Decode :: decode(decoder) ?, CaptchaInfoLen : ::
            bincode :: Decode :: decode(decoder) ?, CaptchaInfo : :: bincode
            :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcRspGenUserCaptchaField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, CaptchaInfoLen : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CaptchaInfo : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}