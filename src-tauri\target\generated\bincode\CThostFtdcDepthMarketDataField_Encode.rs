impl :: bincode :: Encode for CThostFtdcDepthMarketDataField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreSettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreClosePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreOpenInterest, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.HighestPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.LowestPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.Turnover, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenInterest, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClosePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpperLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.LowerLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreDelta, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrDelta, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpdateTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpdateMillisec, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice1, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume1, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice1, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume1, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice2, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume2, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice2, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume2, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice3, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume3, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice3, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume3, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice4, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume4, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice4, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume4, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice5, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume5, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice5, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume5, encoder) ?; :: bincode :: Encode ::
        encode(&self.AveragePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BandingUpperPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.BandingLowerPrice, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}