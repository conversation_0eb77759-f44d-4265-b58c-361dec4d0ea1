impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcStrikeOffsetField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?,
            InvestorRange : :: bincode :: Decode :: decode(decoder) ?,
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, Offset : :: bincode ::
            Decode :: decode(decoder) ?, OffsetType : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcStrikeOffsetField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorRange : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Offset : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OffsetType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstrumentID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}