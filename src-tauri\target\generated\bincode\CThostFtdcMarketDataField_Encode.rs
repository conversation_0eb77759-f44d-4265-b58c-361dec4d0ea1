impl :: bincode :: Encode for CThostFtdcMarketDataField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreSettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreClosePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreOpenInterest, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.HighestPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.LowestPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.Turnover, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenInterest, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClosePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpperLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.LowerLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreDelta, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrDelta, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpdateTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpdateMillisec, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}