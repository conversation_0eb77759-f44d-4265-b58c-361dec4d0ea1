impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBrokerField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, BrokerAbbr :
            :: bincode :: Decode :: decode(decoder) ?, BrokerName : :: bincode
            :: Decode :: decode(decoder) ?, IsActive : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBrokerField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerAbbr : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerName : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IsActive : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}