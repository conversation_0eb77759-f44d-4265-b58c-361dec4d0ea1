impl :: bincode :: Encode for CThostFtdcExchangeOptionSelfCloseActionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ExchangeID, encoder) ?; ::
        bincode :: Encode :: encode(&self.OptionSelfCloseSysID, encoder) ?; ::
        bincode :: Encode :: encode(&self.ActionFlag, encoder) ?; :: bincode
        :: Encode :: encode(&self.ActionDate, encoder) ?; :: bincode :: Encode
        :: encode(&self.ActionTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.TraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OptionSelfCloseLocalID, encoder) ?; :: bincode :: Encode
        :: encode(&self.ActionLocalID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ParticipantID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BusinessUnit, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderActionStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.OptSelfCloseFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}