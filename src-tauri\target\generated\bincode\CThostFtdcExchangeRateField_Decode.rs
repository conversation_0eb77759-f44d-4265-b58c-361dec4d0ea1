impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcExchangeRateField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?,
            FromCurrencyID : :: bincode :: Decode :: decode(decoder) ?,
            FromCurrencyUnit : :: bincode :: Decode :: decode(decoder) ?,
            ToCurrencyID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeRate : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcExchangeRateField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FromCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            FromCurrencyUnit : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ToCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeRate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}