impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcRspUserLoginField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: Decode :: decode(decoder) ?, LoginTime
            : :: bincode :: Decode :: decode(decoder) ?, BrokerID : :: bincode
            :: Decode :: decode(decoder) ?, UserID : :: bincode :: Decode ::
            decode(decoder) ?, SystemName : :: bincode :: Decode ::
            decode(decoder) ?, FrontID : :: bincode :: Decode ::
            decode(decoder) ?, SessionID : :: bincode :: Decode ::
            decode(decoder) ?, MaxOrderRef : :: bincode :: Decode ::
            decode(decoder) ?, SHFETime : :: bincode :: Decode ::
            decode(decoder) ?, DCETime : :: bincode :: Decode ::
            decode(decoder) ?, CZCETime : :: bincode :: Decode ::
            decode(decoder) ?, FFEXTime : :: bincode :: Decode ::
            decode(decoder) ?, INETime : :: bincode :: Decode ::
            decode(decoder) ?, SysVersion : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcRspUserLoginField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LoginTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BrokerID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, SystemName : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FrontID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, SessionID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MaxOrderRef : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SHFETime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, DCETime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CZCETime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, FFEXTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, INETime : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, SysVersion : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}