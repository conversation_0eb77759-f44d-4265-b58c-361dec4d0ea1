impl :: bincode :: Encode for CThostFtdcMarketDataBandingPriceField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BandingUpperPrice, encoder) ?; ::
        bincode :: Encode :: encode(&self.BandingLowerPrice, encoder) ?; core
        :: result :: Result :: Ok(())
    }
}