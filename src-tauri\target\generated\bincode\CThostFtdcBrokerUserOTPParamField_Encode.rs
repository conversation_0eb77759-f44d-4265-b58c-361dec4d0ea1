impl :: bincode :: Encode for CThostFtdcBrokerUserOTPParamField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OTPVendorsID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SerialNumber, encoder) ?; :: bincode :: Encode ::
        encode(&self.Auth<PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastDrift, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastSuccess, encoder) ?; :: bincode :: Encode ::
        encode(&self.OTPType, encoder) ?; core :: result :: Result :: Ok(())
    }
}