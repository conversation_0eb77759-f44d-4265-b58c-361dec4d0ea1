/* automatically generated by rust-bindgen 0.69.5 */

pub const THOST_FTDC_EXP_Normal: u8 = 48u8;
pub const THOST_FTDC_EXP_GenOrderByTrade: u8 = 49u8;
pub const THOST_FTDC_ICT_EID: u8 = 48u8;
pub const THOST_FTDC_ICT_IDCard: u8 = 49u8;
pub const THOST_FTDC_ICT_OfficerIDCard: u8 = 50u8;
pub const THOST_FTDC_ICT_PoliceIDCard: u8 = 51u8;
pub const THOST_FTDC_ICT_SoldierIDCard: u8 = 52u8;
pub const THOST_FTDC_ICT_HouseholdRegister: u8 = 53u8;
pub const THOST_FTDC_ICT_Passport: u8 = 54u8;
pub const THOST_FTDC_ICT_TaiwanCompatriotIDCard: u8 = 55u8;
pub const THOST_FTDC_ICT_HomeComingCard: u8 = 56u8;
pub const THOST_FTDC_ICT_LicenseNo: u8 = 57u8;
pub const THOST_FTDC_ICT_TaxNo: u8 = 65u8;
pub const THOST_FTDC_ICT_HMMainlandTravelPermit: u8 = 66u8;
pub const THOST_FTDC_ICT_TwMainlandTravelPermit: u8 = 67u8;
pub const THOST_FTDC_ICT_DrivingLicense: u8 = 68u8;
pub const THOST_FTDC_ICT_SocialID: u8 = 70u8;
pub const THOST_FTDC_ICT_LocalID: u8 = 71u8;
pub const THOST_FTDC_ICT_BusinessRegistration: u8 = 72u8;
pub const THOST_FTDC_ICT_HKMCIDCard: u8 = 73u8;
pub const THOST_FTDC_ICT_AccountsPermits: u8 = 74u8;
pub const THOST_FTDC_ICT_FrgPrmtRdCard: u8 = 75u8;
pub const THOST_FTDC_ICT_CptMngPrdLetter: u8 = 76u8;
pub const THOST_FTDC_ICT_HKMCTwResidencePermit: u8 = 77u8;
pub const THOST_FTDC_ICT_UniformSocialCreditCode: u8 = 78u8;
pub const THOST_FTDC_ICT_CorporationCertNo: u8 = 79u8;
pub const THOST_FTDC_ICT_OtherCard: u8 = 120u8;
pub const THOST_FTDC_IR_All: u8 = 49u8;
pub const THOST_FTDC_IR_Group: u8 = 50u8;
pub const THOST_FTDC_IR_Single: u8 = 51u8;
pub const THOST_FTDC_DR_All: u8 = 49u8;
pub const THOST_FTDC_DR_Group: u8 = 50u8;
pub const THOST_FTDC_DR_Single: u8 = 51u8;
pub const THOST_FTDC_DS_Asynchronous: u8 = 49u8;
pub const THOST_FTDC_DS_Synchronizing: u8 = 50u8;
pub const THOST_FTDC_DS_Synchronized: u8 = 51u8;
pub const THOST_FTDC_BDS_Synchronized: u8 = 49u8;
pub const THOST_FTDC_BDS_Synchronizing: u8 = 50u8;
pub const THOST_FTDC_ECS_NoConnection: u8 = 49u8;
pub const THOST_FTDC_ECS_QryInstrumentSent: u8 = 50u8;
pub const THOST_FTDC_ECS_GotInformation: u8 = 57u8;
pub const THOST_FTDC_TCS_NotConnected: u8 = 49u8;
pub const THOST_FTDC_TCS_Connected: u8 = 50u8;
pub const THOST_FTDC_TCS_QryInstrumentSent: u8 = 51u8;
pub const THOST_FTDC_TCS_SubPrivateFlow: u8 = 52u8;
pub const THOST_FTDC_FC_DataAsync: u8 = 49u8;
pub const THOST_FTDC_FC_ForceUserLogout: u8 = 50u8;
pub const THOST_FTDC_FC_UserPasswordUpdate: u8 = 51u8;
pub const THOST_FTDC_FC_BrokerPasswordUpdate: u8 = 52u8;
pub const THOST_FTDC_FC_InvestorPasswordUpdate: u8 = 53u8;
pub const THOST_FTDC_FC_OrderInsert: u8 = 54u8;
pub const THOST_FTDC_FC_OrderAction: u8 = 55u8;
pub const THOST_FTDC_FC_SyncSystemData: u8 = 56u8;
pub const THOST_FTDC_FC_SyncBrokerData: u8 = 57u8;
pub const THOST_FTDC_FC_BachSyncBrokerData: u8 = 65u8;
pub const THOST_FTDC_FC_SuperQuery: u8 = 66u8;
pub const THOST_FTDC_FC_ParkedOrderInsert: u8 = 67u8;
pub const THOST_FTDC_FC_ParkedOrderAction: u8 = 68u8;
pub const THOST_FTDC_FC_SyncOTP: u8 = 69u8;
pub const THOST_FTDC_FC_DeleteOrder: u8 = 70u8;
pub const THOST_FTDC_BFC_ForceUserLogout: u8 = 49u8;
pub const THOST_FTDC_BFC_UserPasswordUpdate: u8 = 50u8;
pub const THOST_FTDC_BFC_SyncBrokerData: u8 = 51u8;
pub const THOST_FTDC_BFC_BachSyncBrokerData: u8 = 52u8;
pub const THOST_FTDC_BFC_OrderInsert: u8 = 53u8;
pub const THOST_FTDC_BFC_OrderAction: u8 = 54u8;
pub const THOST_FTDC_BFC_AllQuery: u8 = 55u8;
pub const THOST_FTDC_BFC_log: u8 = 97u8;
pub const THOST_FTDC_BFC_BaseQry: u8 = 98u8;
pub const THOST_FTDC_BFC_TradeQry: u8 = 99u8;
pub const THOST_FTDC_BFC_Trade: u8 = 100u8;
pub const THOST_FTDC_BFC_Virement: u8 = 101u8;
pub const THOST_FTDC_BFC_Risk: u8 = 102u8;
pub const THOST_FTDC_BFC_Session: u8 = 103u8;
pub const THOST_FTDC_BFC_RiskNoticeCtl: u8 = 104u8;
pub const THOST_FTDC_BFC_RiskNotice: u8 = 105u8;
pub const THOST_FTDC_BFC_BrokerDeposit: u8 = 106u8;
pub const THOST_FTDC_BFC_QueryFund: u8 = 107u8;
pub const THOST_FTDC_BFC_QueryOrder: u8 = 108u8;
pub const THOST_FTDC_BFC_QueryTrade: u8 = 109u8;
pub const THOST_FTDC_BFC_QueryPosition: u8 = 110u8;
pub const THOST_FTDC_BFC_QueryMarketData: u8 = 111u8;
pub const THOST_FTDC_BFC_QueryUserEvent: u8 = 112u8;
pub const THOST_FTDC_BFC_QueryRiskNotify: u8 = 113u8;
pub const THOST_FTDC_BFC_QueryFundChange: u8 = 114u8;
pub const THOST_FTDC_BFC_QueryInvestor: u8 = 115u8;
pub const THOST_FTDC_BFC_QueryTradingCode: u8 = 116u8;
pub const THOST_FTDC_BFC_ForceClose: u8 = 117u8;
pub const THOST_FTDC_BFC_PressTest: u8 = 118u8;
pub const THOST_FTDC_BFC_RemainCalc: u8 = 119u8;
pub const THOST_FTDC_BFC_NetPositionInd: u8 = 120u8;
pub const THOST_FTDC_BFC_RiskPredict: u8 = 121u8;
pub const THOST_FTDC_BFC_DataExport: u8 = 122u8;
pub const THOST_FTDC_BFC_RiskTargetSetup: u8 = 65u8;
pub const THOST_FTDC_BFC_MarketDataWarn: u8 = 66u8;
pub const THOST_FTDC_BFC_QryBizNotice: u8 = 67u8;
pub const THOST_FTDC_BFC_CfgBizNotice: u8 = 68u8;
pub const THOST_FTDC_BFC_SyncOTP: u8 = 69u8;
pub const THOST_FTDC_BFC_SendBizNotice: u8 = 70u8;
pub const THOST_FTDC_BFC_CfgRiskLevelStd: u8 = 71u8;
pub const THOST_FTDC_BFC_TbCommand: u8 = 72u8;
pub const THOST_FTDC_BFC_DeleteOrder: u8 = 74u8;
pub const THOST_FTDC_BFC_ParkedOrderInsert: u8 = 75u8;
pub const THOST_FTDC_BFC_ParkedOrderAction: u8 = 76u8;
pub const THOST_FTDC_BFC_ExecOrderNoCheck: u8 = 77u8;
pub const THOST_FTDC_BFC_Designate: u8 = 78u8;
pub const THOST_FTDC_BFC_StockDisposal: u8 = 79u8;
pub const THOST_FTDC_BFC_BrokerDepositWarn: u8 = 81u8;
pub const THOST_FTDC_BFC_CoverWarn: u8 = 83u8;
pub const THOST_FTDC_BFC_PreExecOrder: u8 = 84u8;
pub const THOST_FTDC_BFC_ExecOrderRisk: u8 = 80u8;
pub const THOST_FTDC_BFC_PosiLimitWarn: u8 = 85u8;
pub const THOST_FTDC_BFC_QryPosiLimit: u8 = 86u8;
pub const THOST_FTDC_BFC_FBSign: u8 = 87u8;
pub const THOST_FTDC_BFC_FBAccount: u8 = 88u8;
pub const THOST_FTDC_OAS_Submitted: u8 = 97u8;
pub const THOST_FTDC_OAS_Accepted: u8 = 98u8;
pub const THOST_FTDC_OAS_Rejected: u8 = 99u8;
pub const THOST_FTDC_OST_AllTraded: u8 = 48u8;
pub const THOST_FTDC_OST_PartTradedQueueing: u8 = 49u8;
pub const THOST_FTDC_OST_PartTradedNotQueueing: u8 = 50u8;
pub const THOST_FTDC_OST_NoTradeQueueing: u8 = 51u8;
pub const THOST_FTDC_OST_NoTradeNotQueueing: u8 = 52u8;
pub const THOST_FTDC_OST_Canceled: u8 = 53u8;
pub const THOST_FTDC_OST_Unknown: u8 = 97u8;
pub const THOST_FTDC_OST_NotTouched: u8 = 98u8;
pub const THOST_FTDC_OST_Touched: u8 = 99u8;
pub const THOST_FTDC_OSS_InsertSubmitted: u8 = 48u8;
pub const THOST_FTDC_OSS_CancelSubmitted: u8 = 49u8;
pub const THOST_FTDC_OSS_ModifySubmitted: u8 = 50u8;
pub const THOST_FTDC_OSS_Accepted: u8 = 51u8;
pub const THOST_FTDC_OSS_InsertRejected: u8 = 52u8;
pub const THOST_FTDC_OSS_CancelRejected: u8 = 53u8;
pub const THOST_FTDC_OSS_ModifyRejected: u8 = 54u8;
pub const THOST_FTDC_PSD_Today: u8 = 49u8;
pub const THOST_FTDC_PSD_History: u8 = 50u8;
pub const THOST_FTDC_PDT_UseHistory: u8 = 49u8;
pub const THOST_FTDC_PDT_NoUseHistory: u8 = 50u8;
pub const THOST_FTDC_ER_Broker: u8 = 49u8;
pub const THOST_FTDC_ER_Host: u8 = 50u8;
pub const THOST_FTDC_ER_Maker: u8 = 51u8;
pub const THOST_FTDC_PC_Futures: u8 = 49u8;
pub const THOST_FTDC_PC_Options: u8 = 50u8;
pub const THOST_FTDC_PC_Combination: u8 = 51u8;
pub const THOST_FTDC_PC_Spot: u8 = 52u8;
pub const THOST_FTDC_PC_EFP: u8 = 53u8;
pub const THOST_FTDC_PC_SpotOption: u8 = 54u8;
pub const THOST_FTDC_PC_TAS: u8 = 55u8;
pub const THOST_FTDC_PC_MI: u8 = 73u8;
pub const THOST_FTDC_APC_FutureSingle: u8 = 49u8;
pub const THOST_FTDC_APC_OptionSingle: u8 = 50u8;
pub const THOST_FTDC_APC_Futures: u8 = 51u8;
pub const THOST_FTDC_APC_Options: u8 = 52u8;
pub const THOST_FTDC_APC_TradingComb: u8 = 53u8;
pub const THOST_FTDC_APC_UnTradingComb: u8 = 54u8;
pub const THOST_FTDC_APC_AllTrading: u8 = 55u8;
pub const THOST_FTDC_APC_All: u8 = 56u8;
pub const THOST_FTDC_IP_NotStart: u8 = 48u8;
pub const THOST_FTDC_IP_Started: u8 = 49u8;
pub const THOST_FTDC_IP_Pause: u8 = 50u8;
pub const THOST_FTDC_IP_Expired: u8 = 51u8;
pub const THOST_FTDC_D_Buy: u8 = 48u8;
pub const THOST_FTDC_D_Sell: u8 = 49u8;
pub const THOST_FTDC_PT_Net: u8 = 49u8;
pub const THOST_FTDC_PT_Gross: u8 = 50u8;
pub const THOST_FTDC_PD_Net: u8 = 49u8;
pub const THOST_FTDC_PD_Long: u8 = 50u8;
pub const THOST_FTDC_PD_Short: u8 = 51u8;
pub const THOST_FTDC_SS_NonActive: u8 = 49u8;
pub const THOST_FTDC_SS_Startup: u8 = 50u8;
pub const THOST_FTDC_SS_Operating: u8 = 51u8;
pub const THOST_FTDC_SS_Settlement: u8 = 52u8;
pub const THOST_FTDC_SS_SettlementFinished: u8 = 53u8;
pub const THOST_FTDC_RA_Trade: u8 = 48u8;
pub const THOST_FTDC_RA_Settlement: u8 = 49u8;
pub const THOST_FTDC_HF_Speculation: u8 = 49u8;
pub const THOST_FTDC_HF_Arbitrage: u8 = 50u8;
pub const THOST_FTDC_HF_Hedge: u8 = 51u8;
pub const THOST_FTDC_HF_MarketMaker: u8 = 53u8;
pub const THOST_FTDC_HF_SpecHedge: u8 = 54u8;
pub const THOST_FTDC_HF_HedgeSpec: u8 = 55u8;
pub const THOST_FTDC_BHF_Speculation: u8 = 49u8;
pub const THOST_FTDC_BHF_Arbitrage: u8 = 50u8;
pub const THOST_FTDC_BHF_Hedge: u8 = 51u8;
pub const THOST_FTDC_CIDT_Speculation: u8 = 49u8;
pub const THOST_FTDC_CIDT_Arbitrage: u8 = 50u8;
pub const THOST_FTDC_CIDT_Hedge: u8 = 51u8;
pub const THOST_FTDC_CIDT_MarketMaker: u8 = 53u8;
pub const THOST_FTDC_OPT_AnyPrice: u8 = 49u8;
pub const THOST_FTDC_OPT_LimitPrice: u8 = 50u8;
pub const THOST_FTDC_OPT_BestPrice: u8 = 51u8;
pub const THOST_FTDC_OPT_LastPrice: u8 = 52u8;
pub const THOST_FTDC_OPT_LastPricePlusOneTicks: u8 = 53u8;
pub const THOST_FTDC_OPT_LastPricePlusTwoTicks: u8 = 54u8;
pub const THOST_FTDC_OPT_LastPricePlusThreeTicks: u8 = 55u8;
pub const THOST_FTDC_OPT_AskPrice1: u8 = 56u8;
pub const THOST_FTDC_OPT_AskPrice1PlusOneTicks: u8 = 57u8;
pub const THOST_FTDC_OPT_AskPrice1PlusTwoTicks: u8 = 65u8;
pub const THOST_FTDC_OPT_AskPrice1PlusThreeTicks: u8 = 66u8;
pub const THOST_FTDC_OPT_BidPrice1: u8 = 67u8;
pub const THOST_FTDC_OPT_BidPrice1PlusOneTicks: u8 = 68u8;
pub const THOST_FTDC_OPT_BidPrice1PlusTwoTicks: u8 = 69u8;
pub const THOST_FTDC_OPT_BidPrice1PlusThreeTicks: u8 = 70u8;
pub const THOST_FTDC_OPT_FiveLevelPrice: u8 = 71u8;
pub const THOST_FTDC_OF_Open: u8 = 48u8;
pub const THOST_FTDC_OF_Close: u8 = 49u8;
pub const THOST_FTDC_OF_ForceClose: u8 = 50u8;
pub const THOST_FTDC_OF_CloseToday: u8 = 51u8;
pub const THOST_FTDC_OF_CloseYesterday: u8 = 52u8;
pub const THOST_FTDC_OF_ForceOff: u8 = 53u8;
pub const THOST_FTDC_OF_LocalForceClose: u8 = 54u8;
pub const THOST_FTDC_FCC_NotForceClose: u8 = 48u8;
pub const THOST_FTDC_FCC_LackDeposit: u8 = 49u8;
pub const THOST_FTDC_FCC_ClientOverPositionLimit: u8 = 50u8;
pub const THOST_FTDC_FCC_MemberOverPositionLimit: u8 = 51u8;
pub const THOST_FTDC_FCC_NotMultiple: u8 = 52u8;
pub const THOST_FTDC_FCC_Violation: u8 = 53u8;
pub const THOST_FTDC_FCC_Other: u8 = 54u8;
pub const THOST_FTDC_FCC_PersonDeliv: u8 = 55u8;
pub const THOST_FTDC_FCC_Notverifycapital: u8 = 56u8;
pub const THOST_FTDC_ORDT_Normal: u8 = 48u8;
pub const THOST_FTDC_ORDT_DeriveFromQuote: u8 = 49u8;
pub const THOST_FTDC_ORDT_DeriveFromCombination: u8 = 50u8;
pub const THOST_FTDC_ORDT_Combination: u8 = 51u8;
pub const THOST_FTDC_ORDT_ConditionalOrder: u8 = 52u8;
pub const THOST_FTDC_ORDT_Swap: u8 = 53u8;
pub const THOST_FTDC_ORDT_DeriveFromBlockTrade: u8 = 54u8;
pub const THOST_FTDC_ORDT_DeriveFromEFPTrade: u8 = 55u8;
pub const THOST_FTDC_TC_IOC: u8 = 49u8;
pub const THOST_FTDC_TC_GFS: u8 = 50u8;
pub const THOST_FTDC_TC_GFD: u8 = 51u8;
pub const THOST_FTDC_TC_GTD: u8 = 52u8;
pub const THOST_FTDC_TC_GTC: u8 = 53u8;
pub const THOST_FTDC_TC_GFA: u8 = 54u8;
pub const THOST_FTDC_VC_AV: u8 = 49u8;
pub const THOST_FTDC_VC_MV: u8 = 50u8;
pub const THOST_FTDC_VC_CV: u8 = 51u8;
pub const THOST_FTDC_CC_Immediately: u8 = 49u8;
pub const THOST_FTDC_CC_Touch: u8 = 50u8;
pub const THOST_FTDC_CC_TouchProfit: u8 = 51u8;
pub const THOST_FTDC_CC_ParkedOrder: u8 = 52u8;
pub const THOST_FTDC_CC_LastPriceGreaterThanStopPrice: u8 = 53u8;
pub const THOST_FTDC_CC_LastPriceGreaterEqualStopPrice: u8 = 54u8;
pub const THOST_FTDC_CC_LastPriceLesserThanStopPrice: u8 = 55u8;
pub const THOST_FTDC_CC_LastPriceLesserEqualStopPrice: u8 = 56u8;
pub const THOST_FTDC_CC_AskPriceGreaterThanStopPrice: u8 = 57u8;
pub const THOST_FTDC_CC_AskPriceGreaterEqualStopPrice: u8 = 65u8;
pub const THOST_FTDC_CC_AskPriceLesserThanStopPrice: u8 = 66u8;
pub const THOST_FTDC_CC_AskPriceLesserEqualStopPrice: u8 = 67u8;
pub const THOST_FTDC_CC_BidPriceGreaterThanStopPrice: u8 = 68u8;
pub const THOST_FTDC_CC_BidPriceGreaterEqualStopPrice: u8 = 69u8;
pub const THOST_FTDC_CC_BidPriceLesserThanStopPrice: u8 = 70u8;
pub const THOST_FTDC_CC_BidPriceLesserEqualStopPrice: u8 = 72u8;
pub const THOST_FTDC_AF_Delete: u8 = 48u8;
pub const THOST_FTDC_AF_Modify: u8 = 51u8;
pub const THOST_FTDC_TR_Allow: u8 = 48u8;
pub const THOST_FTDC_TR_CloseOnly: u8 = 49u8;
pub const THOST_FTDC_TR_Forbidden: u8 = 50u8;
pub const THOST_FTDC_OSRC_Participant: u8 = 48u8;
pub const THOST_FTDC_OSRC_Administrator: u8 = 49u8;
pub const THOST_FTDC_TRDT_SplitCombination: u8 = 35u8;
pub const THOST_FTDC_TRDT_Common: u8 = 48u8;
pub const THOST_FTDC_TRDT_OptionsExecution: u8 = 49u8;
pub const THOST_FTDC_TRDT_OTC: u8 = 50u8;
pub const THOST_FTDC_TRDT_EFPDerived: u8 = 51u8;
pub const THOST_FTDC_TRDT_CombinationDerived: u8 = 52u8;
pub const THOST_FTDC_TRDT_BlockTrade: u8 = 53u8;
pub const THOST_FTDC_SPOST_Common: u8 = 35u8;
pub const THOST_FTDC_SPOST_Tas: u8 = 48u8;
pub const THOST_FTDC_PSRC_LastPrice: u8 = 48u8;
pub const THOST_FTDC_PSRC_Buy: u8 = 49u8;
pub const THOST_FTDC_PSRC_Sell: u8 = 50u8;
pub const THOST_FTDC_PSRC_OTC: u8 = 51u8;
pub const THOST_FTDC_IS_BeforeTrading: u8 = 48u8;
pub const THOST_FTDC_IS_NoTrading: u8 = 49u8;
pub const THOST_FTDC_IS_Continous: u8 = 50u8;
pub const THOST_FTDC_IS_AuctionOrdering: u8 = 51u8;
pub const THOST_FTDC_IS_AuctionBalance: u8 = 52u8;
pub const THOST_FTDC_IS_AuctionMatch: u8 = 53u8;
pub const THOST_FTDC_IS_Closed: u8 = 54u8;
pub const THOST_FTDC_IER_Automatic: u8 = 49u8;
pub const THOST_FTDC_IER_Manual: u8 = 50u8;
pub const THOST_FTDC_IER_Fuse: u8 = 51u8;
pub const THOST_FTDC_BS_NoUpload: u8 = 49u8;
pub const THOST_FTDC_BS_Uploaded: u8 = 50u8;
pub const THOST_FTDC_BS_Failed: u8 = 51u8;
pub const THOST_FTDC_RS_All: u8 = 49u8;
pub const THOST_FTDC_RS_ByProduct: u8 = 50u8;
pub const THOST_FTDC_RP_ByVolume: u8 = 49u8;
pub const THOST_FTDC_RP_ByFeeOnHand: u8 = 50u8;
pub const THOST_FTDC_RL_Level1: u8 = 49u8;
pub const THOST_FTDC_RL_Level2: u8 = 50u8;
pub const THOST_FTDC_RL_Level3: u8 = 51u8;
pub const THOST_FTDC_RL_Level4: u8 = 52u8;
pub const THOST_FTDC_RL_Level5: u8 = 53u8;
pub const THOST_FTDC_RL_Level6: u8 = 54u8;
pub const THOST_FTDC_RL_Level7: u8 = 55u8;
pub const THOST_FTDC_RL_Level8: u8 = 56u8;
pub const THOST_FTDC_RL_Level9: u8 = 57u8;
pub const THOST_FTDC_RSD_ByPeriod: u8 = 49u8;
pub const THOST_FTDC_RSD_ByStandard: u8 = 50u8;
pub const THOST_FTDC_MT_Out: u8 = 48u8;
pub const THOST_FTDC_MT_In: u8 = 49u8;
pub const THOST_FTDC_ISPI_MortgageRatio: u8 = 52u8;
pub const THOST_FTDC_ISPI_MarginWay: u8 = 53u8;
pub const THOST_FTDC_ISPI_BillDeposit: u8 = 57u8;
pub const THOST_FTDC_ESPI_MortgageRatio: u8 = 49u8;
pub const THOST_FTDC_ESPI_OtherFundItem: u8 = 50u8;
pub const THOST_FTDC_ESPI_OtherFundImport: u8 = 51u8;
pub const THOST_FTDC_ESPI_CFFEXMinPrepa: u8 = 54u8;
pub const THOST_FTDC_ESPI_CZCESettlementType: u8 = 55u8;
pub const THOST_FTDC_ESPI_ExchDelivFeeMode: u8 = 57u8;
pub const THOST_FTDC_ESPI_DelivFeeMode: u8 = 48u8;
pub const THOST_FTDC_ESPI_CZCEComMarginType: u8 = 65u8;
pub const THOST_FTDC_ESPI_DceComMarginType: u8 = 66u8;
pub const THOST_FTDC_ESPI_OptOutDisCountRate: u8 = 97u8;
pub const THOST_FTDC_ESPI_OptMiniGuarantee: u8 = 98u8;
pub const THOST_FTDC_SPI_InvestorIDMinLength: u8 = 49u8;
pub const THOST_FTDC_SPI_AccountIDMinLength: u8 = 50u8;
pub const THOST_FTDC_SPI_UserRightLogon: u8 = 51u8;
pub const THOST_FTDC_SPI_SettlementBillTrade: u8 = 52u8;
pub const THOST_FTDC_SPI_TradingCode: u8 = 53u8;
pub const THOST_FTDC_SPI_CheckFund: u8 = 54u8;
pub const THOST_FTDC_SPI_CommModelRight: u8 = 55u8;
pub const THOST_FTDC_SPI_MarginModelRight: u8 = 57u8;
pub const THOST_FTDC_SPI_IsStandardActive: u8 = 56u8;
pub const THOST_FTDC_SPI_UploadSettlementFile: u8 = 85u8;
pub const THOST_FTDC_SPI_DownloadCSRCFile: u8 = 68u8;
pub const THOST_FTDC_SPI_SettlementBillFile: u8 = 83u8;
pub const THOST_FTDC_SPI_CSRCOthersFile: u8 = 67u8;
pub const THOST_FTDC_SPI_InvestorPhoto: u8 = 80u8;
pub const THOST_FTDC_SPI_CSRCData: u8 = 82u8;
pub const THOST_FTDC_SPI_InvestorPwdModel: u8 = 73u8;
pub const THOST_FTDC_SPI_CFFEXInvestorSettleFile: u8 = 70u8;
pub const THOST_FTDC_SPI_InvestorIDType: u8 = 97u8;
pub const THOST_FTDC_SPI_FreezeMaxReMain: u8 = 114u8;
pub const THOST_FTDC_SPI_IsSync: u8 = 65u8;
pub const THOST_FTDC_SPI_RelieveOpenLimit: u8 = 79u8;
pub const THOST_FTDC_SPI_IsStandardFreeze: u8 = 88u8;
pub const THOST_FTDC_SPI_CZCENormalProductHedge: u8 = 66u8;
pub const THOST_FTDC_TPID_EncryptionStandard: u8 = 69u8;
pub const THOST_FTDC_TPID_RiskMode: u8 = 82u8;
pub const THOST_FTDC_TPID_RiskModeGlobal: u8 = 71u8;
pub const THOST_FTDC_TPID_modeEncode: u8 = 80u8;
pub const THOST_FTDC_TPID_tickMode: u8 = 84u8;
pub const THOST_FTDC_TPID_SingleUserSessionMaxNum: u8 = 83u8;
pub const THOST_FTDC_TPID_LoginFailMaxNum: u8 = 76u8;
pub const THOST_FTDC_TPID_IsAuthForce: u8 = 65u8;
pub const THOST_FTDC_TPID_IsPosiFreeze: u8 = 70u8;
pub const THOST_FTDC_TPID_IsPosiLimit: u8 = 77u8;
pub const THOST_FTDC_TPID_ForQuoteTimeInterval: u8 = 81u8;
pub const THOST_FTDC_TPID_IsFuturePosiLimit: u8 = 66u8;
pub const THOST_FTDC_TPID_IsFutureOrderFreq: u8 = 67u8;
pub const THOST_FTDC_TPID_IsExecOrderProfit: u8 = 72u8;
pub const THOST_FTDC_TPID_IsCheckBankAcc: u8 = 73u8;
pub const THOST_FTDC_TPID_PasswordDeadLine: u8 = 74u8;
pub const THOST_FTDC_TPID_IsStrongPassword: u8 = 75u8;
pub const THOST_FTDC_TPID_BalanceMorgage: u8 = 97u8;
pub const THOST_FTDC_TPID_MinPwdLen: u8 = 79u8;
pub const THOST_FTDC_TPID_LoginFailMaxNumForIP: u8 = 85u8;
pub const THOST_FTDC_TPID_PasswordPeriod: u8 = 86u8;
pub const THOST_FTDC_FI_SettlementFund: u8 = 70u8;
pub const THOST_FTDC_FI_Trade: u8 = 84u8;
pub const THOST_FTDC_FI_InvestorPosition: u8 = 80u8;
pub const THOST_FTDC_FI_SubEntryFund: u8 = 79u8;
pub const THOST_FTDC_FI_CZCECombinationPos: u8 = 67u8;
pub const THOST_FTDC_FI_CSRCData: u8 = 82u8;
pub const THOST_FTDC_FI_CZCEClose: u8 = 76u8;
pub const THOST_FTDC_FI_CZCENoClose: u8 = 78u8;
pub const THOST_FTDC_FI_PositionDtl: u8 = 68u8;
pub const THOST_FTDC_FI_OptionStrike: u8 = 83u8;
pub const THOST_FTDC_FI_SettlementPriceComparison: u8 = 77u8;
pub const THOST_FTDC_FI_NonTradePosChange: u8 = 66u8;
pub const THOST_FTDC_FUT_Settlement: u8 = 48u8;
pub const THOST_FTDC_FUT_Check: u8 = 49u8;
pub const THOST_FTDC_FFT_Txt: u8 = 48u8;
pub const THOST_FTDC_FFT_Zip: u8 = 49u8;
pub const THOST_FTDC_FFT_DBF: u8 = 50u8;
pub const THOST_FTDC_FUS_SucceedUpload: u8 = 49u8;
pub const THOST_FTDC_FUS_FailedUpload: u8 = 50u8;
pub const THOST_FTDC_FUS_SucceedLoad: u8 = 51u8;
pub const THOST_FTDC_FUS_PartSucceedLoad: u8 = 52u8;
pub const THOST_FTDC_FUS_FailedLoad: u8 = 53u8;
pub const THOST_FTDC_TD_Out: u8 = 48u8;
pub const THOST_FTDC_TD_In: u8 = 49u8;
pub const THOST_FTDC_SC_NoSpecialRule: u8 = 48u8;
pub const THOST_FTDC_SC_NoSpringFestival: u8 = 49u8;
pub const THOST_FTDC_IPT_LastSettlement: u8 = 49u8;
pub const THOST_FTDC_IPT_LaseClose: u8 = 50u8;
pub const THOST_FTDC_PLP_Active: u8 = 49u8;
pub const THOST_FTDC_PLP_NonActive: u8 = 50u8;
pub const THOST_FTDC_PLP_Canceled: u8 = 51u8;
pub const THOST_FTDC_DM_CashDeliv: u8 = 49u8;
pub const THOST_FTDC_DM_CommodityDeliv: u8 = 50u8;
pub const THOST_FTDC_FIOT_FundIO: u8 = 49u8;
pub const THOST_FTDC_FIOT_Transfer: u8 = 50u8;
pub const THOST_FTDC_FIOT_SwapCurrency: u8 = 51u8;
pub const THOST_FTDC_FT_Deposite: u8 = 49u8;
pub const THOST_FTDC_FT_ItemFund: u8 = 50u8;
pub const THOST_FTDC_FT_Company: u8 = 51u8;
pub const THOST_FTDC_FT_InnerTransfer: u8 = 52u8;
pub const THOST_FTDC_FD_In: u8 = 49u8;
pub const THOST_FTDC_FD_Out: u8 = 50u8;
pub const THOST_FTDC_FS_Record: u8 = 49u8;
pub const THOST_FTDC_FS_Check: u8 = 50u8;
pub const THOST_FTDC_FS_Charge: u8 = 51u8;
pub const THOST_FTDC_PS_None: u8 = 49u8;
pub const THOST_FTDC_PS_Publishing: u8 = 50u8;
pub const THOST_FTDC_PS_Published: u8 = 51u8;
pub const THOST_FTDC_ES_NonActive: u8 = 49u8;
pub const THOST_FTDC_ES_Startup: u8 = 50u8;
pub const THOST_FTDC_ES_Initialize: u8 = 51u8;
pub const THOST_FTDC_ES_Initialized: u8 = 52u8;
pub const THOST_FTDC_ES_Close: u8 = 53u8;
pub const THOST_FTDC_ES_Closed: u8 = 54u8;
pub const THOST_FTDC_ES_Settlement: u8 = 55u8;
pub const THOST_FTDC_STS_Initialize: u8 = 48u8;
pub const THOST_FTDC_STS_Settlementing: u8 = 49u8;
pub const THOST_FTDC_STS_Settlemented: u8 = 50u8;
pub const THOST_FTDC_STS_Finished: u8 = 51u8;
pub const THOST_FTDC_CT_Person: u8 = 48u8;
pub const THOST_FTDC_CT_Company: u8 = 49u8;
pub const THOST_FTDC_CT_Fund: u8 = 50u8;
pub const THOST_FTDC_CT_SpecialOrgan: u8 = 51u8;
pub const THOST_FTDC_CT_Asset: u8 = 52u8;
pub const THOST_FTDC_BT_Trade: u8 = 48u8;
pub const THOST_FTDC_BT_TradeSettle: u8 = 49u8;
pub const THOST_FTDC_FAS_Low: u8 = 49u8;
pub const THOST_FTDC_FAS_Normal: u8 = 50u8;
pub const THOST_FTDC_FAS_Focus: u8 = 51u8;
pub const THOST_FTDC_FAS_Risk: u8 = 52u8;
pub const THOST_FTDC_FAS_ByTrade: u8 = 49u8;
pub const THOST_FTDC_FAS_ByDeliv: u8 = 50u8;
pub const THOST_FTDC_FAS_None: u8 = 51u8;
pub const THOST_FTDC_FAS_FixFee: u8 = 52u8;
pub const THOST_FTDC_PWDT_Trade: u8 = 49u8;
pub const THOST_FTDC_PWDT_Account: u8 = 50u8;
pub const THOST_FTDC_AG_All: u8 = 49u8;
pub const THOST_FTDC_AG_OnlyLost: u8 = 50u8;
pub const THOST_FTDC_AG_OnlyGain: u8 = 51u8;
pub const THOST_FTDC_AG_None: u8 = 52u8;
pub const THOST_FTDC_ICP_Include: u8 = 48u8;
pub const THOST_FTDC_ICP_NotInclude: u8 = 50u8;
pub const THOST_FTDC_AWT_Enable: u8 = 48u8;
pub const THOST_FTDC_AWT_Disable: u8 = 50u8;
pub const THOST_FTDC_AWT_NoHoldEnable: u8 = 51u8;
pub const THOST_FTDC_FPWD_UnCheck: u8 = 48u8;
pub const THOST_FTDC_FPWD_Check: u8 = 49u8;
pub const THOST_FTDC_TT_BankToFuture: u8 = 48u8;
pub const THOST_FTDC_TT_FutureToBank: u8 = 49u8;
pub const THOST_FTDC_TVF_Invalid: u8 = 48u8;
pub const THOST_FTDC_TVF_Valid: u8 = 49u8;
pub const THOST_FTDC_TVF_Reverse: u8 = 50u8;
pub const THOST_FTDC_RN_CD: u8 = 48u8;
pub const THOST_FTDC_RN_ZT: u8 = 49u8;
pub const THOST_FTDC_RN_QT: u8 = 50u8;
pub const THOST_FTDC_SEX_None: u8 = 48u8;
pub const THOST_FTDC_SEX_Man: u8 = 49u8;
pub const THOST_FTDC_SEX_Woman: u8 = 50u8;
pub const THOST_FTDC_UT_Investor: u8 = 48u8;
pub const THOST_FTDC_UT_Operator: u8 = 49u8;
pub const THOST_FTDC_UT_SuperUser: u8 = 50u8;
pub const THOST_FTDC_RATETYPE_MarginRate: u8 = 50u8;
pub const THOST_FTDC_NOTETYPE_TradeSettleBill: u8 = 49u8;
pub const THOST_FTDC_NOTETYPE_TradeSettleMonth: u8 = 50u8;
pub const THOST_FTDC_NOTETYPE_CallMarginNotes: u8 = 51u8;
pub const THOST_FTDC_NOTETYPE_ForceCloseNotes: u8 = 52u8;
pub const THOST_FTDC_NOTETYPE_TradeNotes: u8 = 53u8;
pub const THOST_FTDC_NOTETYPE_DelivNotes: u8 = 54u8;
pub const THOST_FTDC_SBS_Day: u8 = 49u8;
pub const THOST_FTDC_SBS_Volume: u8 = 50u8;
pub const THOST_FTDC_ST_Day: u8 = 48u8;
pub const THOST_FTDC_ST_Month: u8 = 49u8;
pub const THOST_FTDC_URT_Logon: u8 = 49u8;
pub const THOST_FTDC_URT_Transfer: u8 = 50u8;
pub const THOST_FTDC_URT_EMail: u8 = 51u8;
pub const THOST_FTDC_URT_Fax: u8 = 52u8;
pub const THOST_FTDC_URT_ConditionOrder: u8 = 53u8;
pub const THOST_FTDC_MPT_PreSettlementPrice: u8 = 49u8;
pub const THOST_FTDC_MPT_SettlementPrice: u8 = 50u8;
pub const THOST_FTDC_MPT_AveragePrice: u8 = 51u8;
pub const THOST_FTDC_MPT_OpenPrice: u8 = 52u8;
pub const THOST_FTDC_BGS_None: u8 = 48u8;
pub const THOST_FTDC_BGS_NoGenerated: u8 = 49u8;
pub const THOST_FTDC_BGS_Generated: u8 = 50u8;
pub const THOST_FTDC_AT_HandlePositionAlgo: u8 = 49u8;
pub const THOST_FTDC_AT_FindMarginRateAlgo: u8 = 50u8;
pub const THOST_FTDC_HPA_Base: u8 = 49u8;
pub const THOST_FTDC_HPA_DCE: u8 = 50u8;
pub const THOST_FTDC_HPA_CZCE: u8 = 51u8;
pub const THOST_FTDC_FMRA_Base: u8 = 49u8;
pub const THOST_FTDC_FMRA_DCE: u8 = 50u8;
pub const THOST_FTDC_FMRA_CZCE: u8 = 51u8;
pub const THOST_FTDC_HTAA_Base: u8 = 49u8;
pub const THOST_FTDC_HTAA_DCE: u8 = 50u8;
pub const THOST_FTDC_HTAA_CZCE: u8 = 51u8;
pub const THOST_FTDC_PST_Order: u8 = 49u8;
pub const THOST_FTDC_PST_Open: u8 = 50u8;
pub const THOST_FTDC_PST_Fund: u8 = 51u8;
pub const THOST_FTDC_PST_Settlement: u8 = 52u8;
pub const THOST_FTDC_PST_Company: u8 = 53u8;
pub const THOST_FTDC_PST_Corporation: u8 = 54u8;
pub const THOST_FTDC_PST_LinkMan: u8 = 55u8;
pub const THOST_FTDC_PST_Ledger: u8 = 56u8;
pub const THOST_FTDC_PST_Trustee: u8 = 57u8;
pub const THOST_FTDC_PST_TrusteeCorporation: u8 = 65u8;
pub const THOST_FTDC_PST_TrusteeOpen: u8 = 66u8;
pub const THOST_FTDC_PST_TrusteeContact: u8 = 67u8;
pub const THOST_FTDC_PST_ForeignerRefer: u8 = 68u8;
pub const THOST_FTDC_PST_CorporationRefer: u8 = 69u8;
pub const THOST_FTDC_QIR_All: u8 = 49u8;
pub const THOST_FTDC_QIR_Group: u8 = 50u8;
pub const THOST_FTDC_QIR_Single: u8 = 51u8;
pub const THOST_FTDC_IRS_Normal: u8 = 49u8;
pub const THOST_FTDC_IRS_Warn: u8 = 50u8;
pub const THOST_FTDC_IRS_Call: u8 = 51u8;
pub const THOST_FTDC_IRS_Force: u8 = 52u8;
pub const THOST_FTDC_IRS_Exception: u8 = 53u8;
pub const THOST_FTDC_UET_Login: u8 = 49u8;
pub const THOST_FTDC_UET_Logout: u8 = 50u8;
pub const THOST_FTDC_UET_Trading: u8 = 51u8;
pub const THOST_FTDC_UET_TradingError: u8 = 52u8;
pub const THOST_FTDC_UET_UpdatePassword: u8 = 53u8;
pub const THOST_FTDC_UET_Authenticate: u8 = 54u8;
pub const THOST_FTDC_UET_SubmitSysInfo: u8 = 55u8;
pub const THOST_FTDC_UET_Transfer: u8 = 56u8;
pub const THOST_FTDC_UET_Other: u8 = 57u8;
pub const THOST_FTDC_ICS_Close: u8 = 48u8;
pub const THOST_FTDC_ICS_CloseToday: u8 = 49u8;
pub const THOST_FTDC_SM_Non: u8 = 48u8;
pub const THOST_FTDC_SM_Instrument: u8 = 49u8;
pub const THOST_FTDC_SM_Product: u8 = 50u8;
pub const THOST_FTDC_SM_Investor: u8 = 51u8;
pub const THOST_FTDC_PAOS_NotSend: u8 = 49u8;
pub const THOST_FTDC_PAOS_Send: u8 = 50u8;
pub const THOST_FTDC_PAOS_Deleted: u8 = 51u8;
pub const THOST_FTDC_VDS_Dealing: u8 = 49u8;
pub const THOST_FTDC_VDS_DeaclSucceed: u8 = 50u8;
pub const THOST_FTDC_ORGS_Standard: u8 = 48u8;
pub const THOST_FTDC_ORGS_ESunny: u8 = 49u8;
pub const THOST_FTDC_ORGS_KingStarV6: u8 = 50u8;
pub const THOST_FTDC_VTS_NaturalDeal: u8 = 48u8;
pub const THOST_FTDC_VTS_SucceedEnd: u8 = 49u8;
pub const THOST_FTDC_VTS_FailedEND: u8 = 50u8;
pub const THOST_FTDC_VTS_Exception: u8 = 51u8;
pub const THOST_FTDC_VTS_ManualDeal: u8 = 52u8;
pub const THOST_FTDC_VTS_MesException: u8 = 53u8;
pub const THOST_FTDC_VTS_SysException: u8 = 54u8;
pub const THOST_FTDC_VBAT_BankBook: u8 = 49u8;
pub const THOST_FTDC_VBAT_BankCard: u8 = 50u8;
pub const THOST_FTDC_VBAT_CreditCard: u8 = 51u8;
pub const THOST_FTDC_VMS_Natural: u8 = 48u8;
pub const THOST_FTDC_VMS_Canceled: u8 = 57u8;
pub const THOST_FTDC_VAA_NoAvailAbility: u8 = 48u8;
pub const THOST_FTDC_VAA_AvailAbility: u8 = 49u8;
pub const THOST_FTDC_VAA_Repeal: u8 = 50u8;
pub const THOST_FTDC_GEN_Program: u8 = 48u8;
pub const THOST_FTDC_GEN_HandWork: u8 = 49u8;
pub const THOST_FTDC_CFMMCKK_REQUEST: u8 = 82u8;
pub const THOST_FTDC_CFMMCKK_AUTO: u8 = 65u8;
pub const THOST_FTDC_CFMMCKK_MANUAL: u8 = 77u8;
pub const THOST_FTDC_CFT_IDCard: u8 = 48u8;
pub const THOST_FTDC_CFT_Passport: u8 = 49u8;
pub const THOST_FTDC_CFT_OfficerIDCard: u8 = 50u8;
pub const THOST_FTDC_CFT_SoldierIDCard: u8 = 51u8;
pub const THOST_FTDC_CFT_HomeComingCard: u8 = 52u8;
pub const THOST_FTDC_CFT_HouseholdRegister: u8 = 53u8;
pub const THOST_FTDC_CFT_LicenseNo: u8 = 54u8;
pub const THOST_FTDC_CFT_InstitutionCodeCard: u8 = 55u8;
pub const THOST_FTDC_CFT_TempLicenseNo: u8 = 56u8;
pub const THOST_FTDC_CFT_NoEnterpriseLicenseNo: u8 = 57u8;
pub const THOST_FTDC_CFT_OtherCard: u8 = 120u8;
pub const THOST_FTDC_CFT_SuperDepAgree: u8 = 97u8;
pub const THOST_FTDC_FBC_Others: u8 = 48u8;
pub const THOST_FTDC_FBC_TransferDetails: u8 = 49u8;
pub const THOST_FTDC_FBC_CustAccStatus: u8 = 50u8;
pub const THOST_FTDC_FBC_AccountTradeDetails: u8 = 51u8;
pub const THOST_FTDC_FBC_FutureAccountChangeInfoDetails: u8 = 52u8;
pub const THOST_FTDC_FBC_CustMoneyDetail: u8 = 53u8;
pub const THOST_FTDC_FBC_CustCancelAccountInfo: u8 = 54u8;
pub const THOST_FTDC_FBC_CustMoneyResult: u8 = 55u8;
pub const THOST_FTDC_FBC_OthersExceptionResult: u8 = 56u8;
pub const THOST_FTDC_FBC_CustInterestNetMoneyDetails: u8 = 57u8;
pub const THOST_FTDC_FBC_CustMoneySendAndReceiveDetails: u8 = 97u8;
pub const THOST_FTDC_FBC_CorporationMoneyTotal: u8 = 98u8;
pub const THOST_FTDC_FBC_MainbodyMoneyTotal: u8 = 99u8;
pub const THOST_FTDC_FBC_MainPartMonitorData: u8 = 100u8;
pub const THOST_FTDC_FBC_PreparationMoney: u8 = 101u8;
pub const THOST_FTDC_FBC_BankMoneyMonitorData: u8 = 102u8;
pub const THOST_FTDC_CEC_Exchange: u8 = 49u8;
pub const THOST_FTDC_CEC_Cash: u8 = 50u8;
pub const THOST_FTDC_YNI_Yes: u8 = 48u8;
pub const THOST_FTDC_YNI_No: u8 = 49u8;
pub const THOST_FTDC_BLT_CurrentMoney: u8 = 48u8;
pub const THOST_FTDC_BLT_UsableMoney: u8 = 49u8;
pub const THOST_FTDC_BLT_FetchableMoney: u8 = 50u8;
pub const THOST_FTDC_BLT_FreezeMoney: u8 = 51u8;
pub const THOST_FTDC_GD_Unknown: u8 = 48u8;
pub const THOST_FTDC_GD_Male: u8 = 49u8;
pub const THOST_FTDC_GD_Female: u8 = 50u8;
pub const THOST_FTDC_FPF_BEN: u8 = 48u8;
pub const THOST_FTDC_FPF_OUR: u8 = 49u8;
pub const THOST_FTDC_FPF_SHA: u8 = 50u8;
pub const THOST_FTDC_PWKT_ExchangeKey: u8 = 48u8;
pub const THOST_FTDC_PWKT_PassWordKey: u8 = 49u8;
pub const THOST_FTDC_PWKT_MACKey: u8 = 50u8;
pub const THOST_FTDC_PWKT_MessageKey: u8 = 51u8;
pub const THOST_FTDC_PWT_Query: u8 = 48u8;
pub const THOST_FTDC_PWT_Fetch: u8 = 49u8;
pub const THOST_FTDC_PWT_Transfer: u8 = 50u8;
pub const THOST_FTDC_PWT_Trade: u8 = 51u8;
pub const THOST_FTDC_EM_NoEncry: u8 = 48u8;
pub const THOST_FTDC_EM_DES: u8 = 49u8;
pub const THOST_FTDC_EM_3DES: u8 = 50u8;
pub const THOST_FTDC_BRF_BankNotNeedRepeal: u8 = 48u8;
pub const THOST_FTDC_BRF_BankWaitingRepeal: u8 = 49u8;
pub const THOST_FTDC_BRF_BankBeenRepealed: u8 = 50u8;
pub const THOST_FTDC_BRORF_BrokerNotNeedRepeal: u8 = 48u8;
pub const THOST_FTDC_BRORF_BrokerWaitingRepeal: u8 = 49u8;
pub const THOST_FTDC_BRORF_BrokerBeenRepealed: u8 = 50u8;
pub const THOST_FTDC_TS_Bank: u8 = 48u8;
pub const THOST_FTDC_TS_Future: u8 = 49u8;
pub const THOST_FTDC_TS_Store: u8 = 50u8;
pub const THOST_FTDC_LF_Yes: u8 = 48u8;
pub const THOST_FTDC_LF_No: u8 = 49u8;
pub const THOST_FTDC_BAS_Normal: u8 = 48u8;
pub const THOST_FTDC_BAS_Freeze: u8 = 49u8;
pub const THOST_FTDC_BAS_ReportLoss: u8 = 50u8;
pub const THOST_FTDC_MAS_Normal: u8 = 48u8;
pub const THOST_FTDC_MAS_Cancel: u8 = 49u8;
pub const THOST_FTDC_MSS_Point: u8 = 48u8;
pub const THOST_FTDC_MSS_PrePoint: u8 = 49u8;
pub const THOST_FTDC_MSS_CancelPoint: u8 = 50u8;
pub const THOST_FTDC_SYT_FutureBankTransfer: u8 = 48u8;
pub const THOST_FTDC_SYT_StockBankTransfer: u8 = 49u8;
pub const THOST_FTDC_SYT_TheThirdPartStore: u8 = 50u8;
pub const THOST_FTDC_TEF_NormalProcessing: u8 = 48u8;
pub const THOST_FTDC_TEF_Success: u8 = 49u8;
pub const THOST_FTDC_TEF_Failed: u8 = 50u8;
pub const THOST_FTDC_TEF_Abnormal: u8 = 51u8;
pub const THOST_FTDC_TEF_ManualProcessedForException: u8 = 52u8;
pub const THOST_FTDC_TEF_CommuFailedNeedManualProcess: u8 = 53u8;
pub const THOST_FTDC_TEF_SysErrorNeedManualProcess: u8 = 54u8;
pub const THOST_FTDC_PSS_NotProcess: u8 = 48u8;
pub const THOST_FTDC_PSS_StartProcess: u8 = 49u8;
pub const THOST_FTDC_PSS_Finished: u8 = 50u8;
pub const THOST_FTDC_CUSTT_Person: u8 = 48u8;
pub const THOST_FTDC_CUSTT_Institution: u8 = 49u8;
pub const THOST_FTDC_FBTTD_FromBankToFuture: u8 = 49u8;
pub const THOST_FTDC_FBTTD_FromFutureToBank: u8 = 50u8;
pub const THOST_FTDC_OOD_Open: u8 = 49u8;
pub const THOST_FTDC_OOD_Destroy: u8 = 48u8;
pub const THOST_FTDC_AVAF_Invalid: u8 = 48u8;
pub const THOST_FTDC_AVAF_Valid: u8 = 49u8;
pub const THOST_FTDC_AVAF_Repeal: u8 = 50u8;
pub const THOST_FTDC_OT_Bank: u8 = 49u8;
pub const THOST_FTDC_OT_Future: u8 = 50u8;
pub const THOST_FTDC_OT_PlateForm: u8 = 57u8;
pub const THOST_FTDC_OL_HeadQuarters: u8 = 49u8;
pub const THOST_FTDC_OL_Branch: u8 = 50u8;
pub const THOST_FTDC_PID_FutureProtocal: u8 = 48u8;
pub const THOST_FTDC_PID_ICBCProtocal: u8 = 49u8;
pub const THOST_FTDC_PID_ABCProtocal: u8 = 50u8;
pub const THOST_FTDC_PID_CBCProtocal: u8 = 51u8;
pub const THOST_FTDC_PID_CCBProtocal: u8 = 52u8;
pub const THOST_FTDC_PID_BOCOMProtocal: u8 = 53u8;
pub const THOST_FTDC_PID_FBTPlateFormProtocal: u8 = 88u8;
pub const THOST_FTDC_CM_ShortConnect: u8 = 48u8;
pub const THOST_FTDC_CM_LongConnect: u8 = 49u8;
pub const THOST_FTDC_SRM_ASync: u8 = 48u8;
pub const THOST_FTDC_SRM_Sync: u8 = 49u8;
pub const THOST_FTDC_BAT_BankBook: u8 = 49u8;
pub const THOST_FTDC_BAT_SavingCard: u8 = 50u8;
pub const THOST_FTDC_BAT_CreditCard: u8 = 51u8;
pub const THOST_FTDC_FAT_BankBook: u8 = 49u8;
pub const THOST_FTDC_FAT_SavingCard: u8 = 50u8;
pub const THOST_FTDC_FAT_CreditCard: u8 = 51u8;
pub const THOST_FTDC_OS_Ready: u8 = 48u8;
pub const THOST_FTDC_OS_CheckIn: u8 = 49u8;
pub const THOST_FTDC_OS_CheckOut: u8 = 50u8;
pub const THOST_FTDC_OS_CheckFileArrived: u8 = 51u8;
pub const THOST_FTDC_OS_CheckDetail: u8 = 52u8;
pub const THOST_FTDC_OS_DayEndClean: u8 = 53u8;
pub const THOST_FTDC_OS_Invalid: u8 = 57u8;
pub const THOST_FTDC_CCBFM_ByAmount: u8 = 49u8;
pub const THOST_FTDC_CCBFM_ByMonth: u8 = 50u8;
pub const THOST_FTDC_CAPIT_Client: u8 = 49u8;
pub const THOST_FTDC_CAPIT_Server: u8 = 50u8;
pub const THOST_FTDC_CAPIT_UserApi: u8 = 51u8;
pub const THOST_FTDC_LS_Connected: u8 = 49u8;
pub const THOST_FTDC_LS_Disconnected: u8 = 50u8;
pub const THOST_FTDC_BPWDF_NoCheck: u8 = 48u8;
pub const THOST_FTDC_BPWDF_BlankCheck: u8 = 49u8;
pub const THOST_FTDC_BPWDF_EncryptCheck: u8 = 50u8;
pub const THOST_FTDC_SAT_AccountID: u8 = 49u8;
pub const THOST_FTDC_SAT_CardID: u8 = 50u8;
pub const THOST_FTDC_SAT_SHStockholderID: u8 = 51u8;
pub const THOST_FTDC_SAT_SZStockholderID: u8 = 52u8;
pub const THOST_FTDC_TRFS_Normal: u8 = 48u8;
pub const THOST_FTDC_TRFS_Repealed: u8 = 49u8;
pub const THOST_FTDC_SPTYPE_Broker: u8 = 48u8;
pub const THOST_FTDC_SPTYPE_Bank: u8 = 49u8;
pub const THOST_FTDC_REQRSP_Request: u8 = 48u8;
pub const THOST_FTDC_REQRSP_Response: u8 = 49u8;
pub const THOST_FTDC_FBTUET_SignIn: u8 = 48u8;
pub const THOST_FTDC_FBTUET_FromBankToFuture: u8 = 49u8;
pub const THOST_FTDC_FBTUET_FromFutureToBank: u8 = 50u8;
pub const THOST_FTDC_FBTUET_OpenAccount: u8 = 51u8;
pub const THOST_FTDC_FBTUET_CancelAccount: u8 = 52u8;
pub const THOST_FTDC_FBTUET_ChangeAccount: u8 = 53u8;
pub const THOST_FTDC_FBTUET_RepealFromBankToFuture: u8 = 54u8;
pub const THOST_FTDC_FBTUET_RepealFromFutureToBank: u8 = 55u8;
pub const THOST_FTDC_FBTUET_QueryBankAccount: u8 = 56u8;
pub const THOST_FTDC_FBTUET_QueryFutureAccount: u8 = 57u8;
pub const THOST_FTDC_FBTUET_SignOut: u8 = 65u8;
pub const THOST_FTDC_FBTUET_SyncKey: u8 = 66u8;
pub const THOST_FTDC_FBTUET_ReserveOpenAccount: u8 = 67u8;
pub const THOST_FTDC_FBTUET_CancelReserveOpenAccount: u8 = 68u8;
pub const THOST_FTDC_FBTUET_ReserveOpenAccountConfirm: u8 = 69u8;
pub const THOST_FTDC_FBTUET_Other: u8 = 90u8;
pub const THOST_FTDC_DBOP_Insert: u8 = 48u8;
pub const THOST_FTDC_DBOP_Update: u8 = 49u8;
pub const THOST_FTDC_DBOP_Delete: u8 = 50u8;
pub const THOST_FTDC_SYNF_Yes: u8 = 48u8;
pub const THOST_FTDC_SYNF_No: u8 = 49u8;
pub const THOST_FTDC_SYNT_OneOffSync: u8 = 48u8;
pub const THOST_FTDC_SYNT_TimerSync: u8 = 49u8;
pub const THOST_FTDC_SYNT_TimerFullSync: u8 = 50u8;
pub const THOST_FTDC_FBEDIR_Settlement: u8 = 48u8;
pub const THOST_FTDC_FBEDIR_Sale: u8 = 49u8;
pub const THOST_FTDC_FBERES_Success: u8 = 48u8;
pub const THOST_FTDC_FBERES_InsufficientBalance: u8 = 49u8;
pub const THOST_FTDC_FBERES_UnknownTrading: u8 = 56u8;
pub const THOST_FTDC_FBERES_Fail: u8 = 120u8;
pub const THOST_FTDC_FBEES_Normal: u8 = 48u8;
pub const THOST_FTDC_FBEES_ReExchange: u8 = 49u8;
pub const THOST_FTDC_FBEFG_DataPackage: u8 = 48u8;
pub const THOST_FTDC_FBEFG_File: u8 = 49u8;
pub const THOST_FTDC_FBEAT_NotTrade: u8 = 48u8;
pub const THOST_FTDC_FBEAT_Trade: u8 = 49u8;
pub const THOST_FTDC_FBEUET_SignIn: u8 = 48u8;
pub const THOST_FTDC_FBEUET_Exchange: u8 = 49u8;
pub const THOST_FTDC_FBEUET_ReExchange: u8 = 50u8;
pub const THOST_FTDC_FBEUET_QueryBankAccount: u8 = 51u8;
pub const THOST_FTDC_FBEUET_QueryExchDetial: u8 = 52u8;
pub const THOST_FTDC_FBEUET_QueryExchSummary: u8 = 53u8;
pub const THOST_FTDC_FBEUET_QueryExchRate: u8 = 54u8;
pub const THOST_FTDC_FBEUET_CheckBankAccount: u8 = 55u8;
pub const THOST_FTDC_FBEUET_SignOut: u8 = 56u8;
pub const THOST_FTDC_FBEUET_Other: u8 = 90u8;
pub const THOST_FTDC_FBERF_UnProcessed: u8 = 48u8;
pub const THOST_FTDC_FBERF_WaitSend: u8 = 49u8;
pub const THOST_FTDC_FBERF_SendSuccess: u8 = 50u8;
pub const THOST_FTDC_FBERF_SendFailed: u8 = 51u8;
pub const THOST_FTDC_FBERF_WaitReSend: u8 = 52u8;
pub const THOST_FTDC_NC_NOERROR: u8 = 48u8;
pub const THOST_FTDC_NC_Warn: u8 = 49u8;
pub const THOST_FTDC_NC_Call: u8 = 50u8;
pub const THOST_FTDC_NC_Force: u8 = 51u8;
pub const THOST_FTDC_NC_CHUANCANG: u8 = 52u8;
pub const THOST_FTDC_NC_Exception: u8 = 53u8;
pub const THOST_FTDC_FCT_Manual: u8 = 48u8;
pub const THOST_FTDC_FCT_Single: u8 = 49u8;
pub const THOST_FTDC_FCT_Group: u8 = 50u8;
pub const THOST_FTDC_RNM_System: u8 = 48u8;
pub const THOST_FTDC_RNM_SMS: u8 = 49u8;
pub const THOST_FTDC_RNM_EMail: u8 = 50u8;
pub const THOST_FTDC_RNM_Manual: u8 = 51u8;
pub const THOST_FTDC_RNS_NotGen: u8 = 48u8;
pub const THOST_FTDC_RNS_Generated: u8 = 49u8;
pub const THOST_FTDC_RNS_SendError: u8 = 50u8;
pub const THOST_FTDC_RNS_SendOk: u8 = 51u8;
pub const THOST_FTDC_RNS_Received: u8 = 52u8;
pub const THOST_FTDC_RNS_Confirmed: u8 = 53u8;
pub const THOST_FTDC_RUE_ExportData: u8 = 48u8;
pub const THOST_FTDC_COST_LastPriceAsc: u8 = 48u8;
pub const THOST_FTDC_COST_LastPriceDesc: u8 = 49u8;
pub const THOST_FTDC_COST_AskPriceAsc: u8 = 50u8;
pub const THOST_FTDC_COST_AskPriceDesc: u8 = 51u8;
pub const THOST_FTDC_COST_BidPriceAsc: u8 = 52u8;
pub const THOST_FTDC_COST_BidPriceDesc: u8 = 53u8;
pub const THOST_FTDC_UOAST_NoSend: u8 = 48u8;
pub const THOST_FTDC_UOAST_Sended: u8 = 49u8;
pub const THOST_FTDC_UOAST_Generated: u8 = 50u8;
pub const THOST_FTDC_UOAST_SendFail: u8 = 51u8;
pub const THOST_FTDC_UOAST_Success: u8 = 52u8;
pub const THOST_FTDC_UOAST_Fail: u8 = 53u8;
pub const THOST_FTDC_UOAST_Cancel: u8 = 54u8;
pub const THOST_FTDC_UOACS_NoApply: u8 = 49u8;
pub const THOST_FTDC_UOACS_Submited: u8 = 50u8;
pub const THOST_FTDC_UOACS_Sended: u8 = 51u8;
pub const THOST_FTDC_UOACS_Success: u8 = 52u8;
pub const THOST_FTDC_UOACS_Refuse: u8 = 53u8;
pub const THOST_FTDC_UOACS_Cancel: u8 = 54u8;
pub const THOST_FTDC_QT_Radio: u8 = 49u8;
pub const THOST_FTDC_QT_Option: u8 = 50u8;
pub const THOST_FTDC_QT_Blank: u8 = 51u8;
pub const THOST_FTDC_BT_Request: u8 = 49u8;
pub const THOST_FTDC_BT_Response: u8 = 50u8;
pub const THOST_FTDC_BT_Notice: u8 = 51u8;
pub const THOST_FTDC_CRC_Success: u8 = 48u8;
pub const THOST_FTDC_CRC_Working: u8 = 49u8;
pub const THOST_FTDC_CRC_InfoFail: u8 = 50u8;
pub const THOST_FTDC_CRC_IDCardFail: u8 = 51u8;
pub const THOST_FTDC_CRC_OtherFail: u8 = 52u8;
pub const THOST_FTDC_CfMMCCT_All: u8 = 48u8;
pub const THOST_FTDC_CfMMCCT_Person: u8 = 49u8;
pub const THOST_FTDC_CfMMCCT_Company: u8 = 50u8;
pub const THOST_FTDC_CfMMCCT_Other: u8 = 51u8;
pub const THOST_FTDC_CfMMCCT_SpecialOrgan: u8 = 52u8;
pub const THOST_FTDC_CfMMCCT_Asset: u8 = 53u8;
pub const THOST_FTDC_EIDT_SHFE: u8 = 83u8;
pub const THOST_FTDC_EIDT_CZCE: u8 = 90u8;
pub const THOST_FTDC_EIDT_DCE: u8 = 68u8;
pub const THOST_FTDC_EIDT_CFFEX: u8 = 74u8;
pub const THOST_FTDC_EIDT_INE: u8 = 78u8;
pub const THOST_FTDC_ECIDT_Hedge: u8 = 49u8;
pub const THOST_FTDC_ECIDT_Arbitrage: u8 = 50u8;
pub const THOST_FTDC_ECIDT_Speculation: u8 = 51u8;
pub const THOST_FTDC_UF_NoUpdate: u8 = 48u8;
pub const THOST_FTDC_UF_Success: u8 = 49u8;
pub const THOST_FTDC_UF_Fail: u8 = 50u8;
pub const THOST_FTDC_UF_TCSuccess: u8 = 51u8;
pub const THOST_FTDC_UF_TCFail: u8 = 52u8;
pub const THOST_FTDC_UF_Cancel: u8 = 53u8;
pub const THOST_FTDC_AOID_OpenInvestor: u8 = 49u8;
pub const THOST_FTDC_AOID_ModifyIDCard: u8 = 50u8;
pub const THOST_FTDC_AOID_ModifyNoIDCard: u8 = 51u8;
pub const THOST_FTDC_AOID_ApplyTradingCode: u8 = 52u8;
pub const THOST_FTDC_AOID_CancelTradingCode: u8 = 53u8;
pub const THOST_FTDC_AOID_CancelInvestor: u8 = 54u8;
pub const THOST_FTDC_AOID_FreezeAccount: u8 = 56u8;
pub const THOST_FTDC_AOID_ActiveFreezeAccount: u8 = 57u8;
pub const THOST_FTDC_ASID_NoComplete: u8 = 49u8;
pub const THOST_FTDC_ASID_Submited: u8 = 50u8;
pub const THOST_FTDC_ASID_Checked: u8 = 51u8;
pub const THOST_FTDC_ASID_Refused: u8 = 52u8;
pub const THOST_FTDC_ASID_Deleted: u8 = 53u8;
pub const THOST_FTDC_UOASM_ByAPI: u8 = 49u8;
pub const THOST_FTDC_UOASM_ByFile: u8 = 50u8;
pub const THOST_FTDC_EvM_ADD: u8 = 49u8;
pub const THOST_FTDC_EvM_UPDATE: u8 = 50u8;
pub const THOST_FTDC_EvM_DELETE: u8 = 51u8;
pub const THOST_FTDC_EvM_CHECK: u8 = 52u8;
pub const THOST_FTDC_EvM_COPY: u8 = 53u8;
pub const THOST_FTDC_EvM_CANCEL: u8 = 54u8;
pub const THOST_FTDC_EvM_Reverse: u8 = 55u8;
pub const THOST_FTDC_UOAA_ASR: u8 = 49u8;
pub const THOST_FTDC_UOAA_ASNR: u8 = 50u8;
pub const THOST_FTDC_UOAA_NSAR: u8 = 51u8;
pub const THOST_FTDC_UOAA_NSR: u8 = 52u8;
pub const THOST_FTDC_EvM_InvestorGroupFlow: u8 = 49u8;
pub const THOST_FTDC_EvM_InvestorRate: u8 = 50u8;
pub const THOST_FTDC_EvM_InvestorCommRateModel: u8 = 51u8;
pub const THOST_FTDC_CL_Zero: u8 = 48u8;
pub const THOST_FTDC_CL_One: u8 = 49u8;
pub const THOST_FTDC_CL_Two: u8 = 50u8;
pub const THOST_FTDC_CHS_Init: u8 = 48u8;
pub const THOST_FTDC_CHS_Checking: u8 = 49u8;
pub const THOST_FTDC_CHS_Checked: u8 = 50u8;
pub const THOST_FTDC_CHS_Refuse: u8 = 51u8;
pub const THOST_FTDC_CHS_Cancel: u8 = 52u8;
pub const THOST_FTDC_CHU_Unused: u8 = 48u8;
pub const THOST_FTDC_CHU_Used: u8 = 49u8;
pub const THOST_FTDC_CHU_Fail: u8 = 50u8;
pub const THOST_FTDC_BAO_ByAccProperty: u8 = 48u8;
pub const THOST_FTDC_BAO_ByFBTransfer: u8 = 49u8;
pub const THOST_FTDC_MBTS_ByInstrument: u8 = 48u8;
pub const THOST_FTDC_MBTS_ByDayInsPrc: u8 = 49u8;
pub const THOST_FTDC_MBTS_ByDayIns: u8 = 50u8;
pub const THOST_FTDC_OTP_NONE: u8 = 48u8;
pub const THOST_FTDC_OTP_TOTP: u8 = 49u8;
pub const THOST_FTDC_OTPS_Unused: u8 = 48u8;
pub const THOST_FTDC_OTPS_Used: u8 = 49u8;
pub const THOST_FTDC_OTPS_Disuse: u8 = 50u8;
pub const THOST_FTDC_BUT_Investor: u8 = 49u8;
pub const THOST_FTDC_BUT_BrokerUser: u8 = 50u8;
pub const THOST_FTDC_FUTT_Commodity: u8 = 49u8;
pub const THOST_FTDC_FUTT_Financial: u8 = 50u8;
pub const THOST_FTDC_FET_Restriction: u8 = 48u8;
pub const THOST_FTDC_FET_TodayRestriction: u8 = 49u8;
pub const THOST_FTDC_FET_Transfer: u8 = 50u8;
pub const THOST_FTDC_FET_Credit: u8 = 51u8;
pub const THOST_FTDC_FET_InvestorWithdrawAlm: u8 = 52u8;
pub const THOST_FTDC_FET_BankRestriction: u8 = 53u8;
pub const THOST_FTDC_FET_Accountregister: u8 = 54u8;
pub const THOST_FTDC_FET_ExchangeFundIO: u8 = 55u8;
pub const THOST_FTDC_FET_InvestorFundIO: u8 = 56u8;
pub const THOST_FTDC_AST_FBTransfer: u8 = 48u8;
pub const THOST_FTDC_AST_ManualEntry: u8 = 49u8;
pub const THOST_FTDC_CST_UnifyAccount: u8 = 48u8;
pub const THOST_FTDC_CST_ManualEntry: u8 = 49u8;
pub const THOST_FTDC_UR_All: u8 = 48u8;
pub const THOST_FTDC_UR_Single: u8 = 49u8;
pub const THOST_FTDC_BG_Investor: u8 = 50u8;
pub const THOST_FTDC_BG_Group: u8 = 49u8;
pub const THOST_FTDC_TSSM_Instrument: u8 = 49u8;
pub const THOST_FTDC_TSSM_Product: u8 = 50u8;
pub const THOST_FTDC_TSSM_Exchange: u8 = 51u8;
pub const THOST_FTDC_ESM_Relative: u8 = 49u8;
pub const THOST_FTDC_ESM_Typical: u8 = 50u8;
pub const THOST_FTDC_RIR_All: u8 = 49u8;
pub const THOST_FTDC_RIR_Model: u8 = 50u8;
pub const THOST_FTDC_RIR_Single: u8 = 51u8;
pub const THOST_FTDC_SDS_Initialize: u8 = 48u8;
pub const THOST_FTDC_SDS_Settlementing: u8 = 49u8;
pub const THOST_FTDC_SDS_Settlemented: u8 = 50u8;
pub const THOST_FTDC_TSRC_NORMAL: u8 = 48u8;
pub const THOST_FTDC_TSRC_QUERY: u8 = 49u8;
pub const THOST_FTDC_FSM_Product: u8 = 49u8;
pub const THOST_FTDC_FSM_Exchange: u8 = 50u8;
pub const THOST_FTDC_FSM_All: u8 = 51u8;
pub const THOST_FTDC_BIR_Property: u8 = 49u8;
pub const THOST_FTDC_BIR_All: u8 = 50u8;
pub const THOST_FTDC_PIR_All: u8 = 49u8;
pub const THOST_FTDC_PIR_Property: u8 = 50u8;
pub const THOST_FTDC_PIR_Single: u8 = 51u8;
pub const THOST_FTDC_FIS_NoCreate: u8 = 48u8;
pub const THOST_FTDC_FIS_Created: u8 = 49u8;
pub const THOST_FTDC_FIS_Failed: u8 = 50u8;
pub const THOST_FTDC_FGS_FileTransmit: u8 = 48u8;
pub const THOST_FTDC_FGS_FileGen: u8 = 49u8;
pub const THOST_FTDC_SoM_Add: u8 = 49u8;
pub const THOST_FTDC_SoM_Update: u8 = 50u8;
pub const THOST_FTDC_SoM_Delete: u8 = 51u8;
pub const THOST_FTDC_SoM_Copy: u8 = 52u8;
pub const THOST_FTDC_SoM_AcTive: u8 = 53u8;
pub const THOST_FTDC_SoM_CanCel: u8 = 54u8;
pub const THOST_FTDC_SoM_ReSet: u8 = 55u8;
pub const THOST_FTDC_SoT_UpdatePassword: u8 = 48u8;
pub const THOST_FTDC_SoT_UserDepartment: u8 = 49u8;
pub const THOST_FTDC_SoT_RoleManager: u8 = 50u8;
pub const THOST_FTDC_SoT_RoleFunction: u8 = 51u8;
pub const THOST_FTDC_SoT_BaseParam: u8 = 52u8;
pub const THOST_FTDC_SoT_SetUserID: u8 = 53u8;
pub const THOST_FTDC_SoT_SetUserRole: u8 = 54u8;
pub const THOST_FTDC_SoT_UserIpRestriction: u8 = 55u8;
pub const THOST_FTDC_SoT_DepartmentManager: u8 = 56u8;
pub const THOST_FTDC_SoT_DepartmentCopy: u8 = 57u8;
pub const THOST_FTDC_SoT_Tradingcode: u8 = 65u8;
pub const THOST_FTDC_SoT_InvestorStatus: u8 = 66u8;
pub const THOST_FTDC_SoT_InvestorAuthority: u8 = 67u8;
pub const THOST_FTDC_SoT_PropertySet: u8 = 68u8;
pub const THOST_FTDC_SoT_ReSetInvestorPasswd: u8 = 69u8;
pub const THOST_FTDC_SoT_InvestorPersonalityInfo: u8 = 70u8;
pub const THOST_FTDC_CSRCQ_Current: u8 = 48u8;
pub const THOST_FTDC_CSRCQ_History: u8 = 49u8;
pub const THOST_FTDC_FRS_Normal: u8 = 49u8;
pub const THOST_FTDC_FRS_Freeze: u8 = 48u8;
pub const THOST_FTDC_STST_Standard: u8 = 48u8;
pub const THOST_FTDC_STST_NonStandard: u8 = 49u8;
pub const THOST_FTDC_RPT_Freeze: u8 = 49u8;
pub const THOST_FTDC_RPT_FreezeActive: u8 = 50u8;
pub const THOST_FTDC_RPT_OpenLimit: u8 = 51u8;
pub const THOST_FTDC_RPT_RelieveOpenLimit: u8 = 52u8;
pub const THOST_FTDC_AMLDS_Normal: u8 = 48u8;
pub const THOST_FTDC_AMLDS_Deleted: u8 = 49u8;
pub const THOST_FTDC_AMLCHS_Init: u8 = 48u8;
pub const THOST_FTDC_AMLCHS_Checking: u8 = 49u8;
pub const THOST_FTDC_AMLCHS_Checked: u8 = 50u8;
pub const THOST_FTDC_AMLCHS_RefuseReport: u8 = 51u8;
pub const THOST_FTDC_AMLDT_DrawDay: u8 = 48u8;
pub const THOST_FTDC_AMLDT_TouchDay: u8 = 49u8;
pub const THOST_FTDC_AMLCL_CheckLevel0: u8 = 48u8;
pub const THOST_FTDC_AMLCL_CheckLevel1: u8 = 49u8;
pub const THOST_FTDC_AMLCL_CheckLevel2: u8 = 50u8;
pub const THOST_FTDC_AMLCL_CheckLevel3: u8 = 51u8;
pub const THOST_FTDC_EFT_CSV: u8 = 48u8;
pub const THOST_FTDC_EFT_EXCEL: u8 = 49u8;
pub const THOST_FTDC_EFT_DBF: u8 = 50u8;
pub const THOST_FTDC_SMT_Before: u8 = 49u8;
pub const THOST_FTDC_SMT_Settlement: u8 = 50u8;
pub const THOST_FTDC_SMT_After: u8 = 51u8;
pub const THOST_FTDC_SMT_Settlemented: u8 = 52u8;
pub const THOST_FTDC_SML_Must: u8 = 49u8;
pub const THOST_FTDC_SML_Alarm: u8 = 50u8;
pub const THOST_FTDC_SML_Prompt: u8 = 51u8;
pub const THOST_FTDC_SML_Ignore: u8 = 52u8;
pub const THOST_FTDC_SMG_Exhcange: u8 = 49u8;
pub const THOST_FTDC_SMG_ASP: u8 = 50u8;
pub const THOST_FTDC_SMG_CSRC: u8 = 51u8;
pub const THOST_FTDC_LUT_Repeatable: u8 = 49u8;
pub const THOST_FTDC_LUT_Unrepeatable: u8 = 50u8;
pub const THOST_FTDC_DAR_Settle: u8 = 49u8;
pub const THOST_FTDC_DAR_Exchange: u8 = 50u8;
pub const THOST_FTDC_DAR_CSRC: u8 = 51u8;
pub const THOST_FTDC_MGT_ExchMarginRate: u8 = 48u8;
pub const THOST_FTDC_MGT_InstrMarginRate: u8 = 49u8;
pub const THOST_FTDC_MGT_InstrMarginRateTrade: u8 = 50u8;
pub const THOST_FTDC_ACT_Intraday: u8 = 49u8;
pub const THOST_FTDC_ACT_Long: u8 = 50u8;
pub const THOST_FTDC_MRT_Exchange: u8 = 49u8;
pub const THOST_FTDC_MRT_Investor: u8 = 50u8;
pub const THOST_FTDC_MRT_InvestorTrade: u8 = 51u8;
pub const THOST_FTDC_BUS_UnBak: u8 = 48u8;
pub const THOST_FTDC_BUS_BakUp: u8 = 49u8;
pub const THOST_FTDC_BUS_BakUped: u8 = 50u8;
pub const THOST_FTDC_BUS_BakFail: u8 = 51u8;
pub const THOST_FTDC_SIS_UnInitialize: u8 = 48u8;
pub const THOST_FTDC_SIS_Initialize: u8 = 49u8;
pub const THOST_FTDC_SIS_Initialized: u8 = 50u8;
pub const THOST_FTDC_SRS_NoCreate: u8 = 48u8;
pub const THOST_FTDC_SRS_Create: u8 = 49u8;
pub const THOST_FTDC_SRS_Created: u8 = 50u8;
pub const THOST_FTDC_SRS_CreateFail: u8 = 51u8;
pub const THOST_FTDC_SSS_UnSaveData: u8 = 48u8;
pub const THOST_FTDC_SSS_SaveDatad: u8 = 49u8;
pub const THOST_FTDC_SAS_UnArchived: u8 = 48u8;
pub const THOST_FTDC_SAS_Archiving: u8 = 49u8;
pub const THOST_FTDC_SAS_Archived: u8 = 50u8;
pub const THOST_FTDC_SAS_ArchiveFail: u8 = 51u8;
pub const THOST_FTDC_CTPT_Unkown: u8 = 48u8;
pub const THOST_FTDC_CTPT_MainCenter: u8 = 49u8;
pub const THOST_FTDC_CTPT_BackUp: u8 = 50u8;
pub const THOST_FTDC_CDT_Normal: u8 = 48u8;
pub const THOST_FTDC_CDT_SpecFirst: u8 = 49u8;
pub const THOST_FTDC_MFUR_None: u8 = 48u8;
pub const THOST_FTDC_MFUR_Margin: u8 = 49u8;
pub const THOST_FTDC_MFUR_All: u8 = 50u8;
pub const THOST_FTDC_MFUR_CNY3: u8 = 51u8;
pub const THOST_FTDC_SPT_CzceHedge: u8 = 49u8;
pub const THOST_FTDC_SPT_IneForeignCurrency: u8 = 50u8;
pub const THOST_FTDC_SPT_DceOpenClose: u8 = 51u8;
pub const THOST_FTDC_FMT_Mortgage: u8 = 49u8;
pub const THOST_FTDC_FMT_Redemption: u8 = 50u8;
pub const THOST_FTDC_ASPI_BaseMargin: u8 = 49u8;
pub const THOST_FTDC_ASPI_LowestInterest: u8 = 50u8;
pub const THOST_FTDC_FMD_In: u8 = 49u8;
pub const THOST_FTDC_FMD_Out: u8 = 50u8;
pub const THOST_FTDC_BT_Profit: u8 = 48u8;
pub const THOST_FTDC_BT_Loss: u8 = 49u8;
pub const THOST_FTDC_BT_Other: u8 = 90u8;
pub const THOST_FTDC_SST_Manual: u8 = 48u8;
pub const THOST_FTDC_SST_Automatic: u8 = 49u8;
pub const THOST_FTDC_CED_Settlement: u8 = 48u8;
pub const THOST_FTDC_CED_Sale: u8 = 49u8;
pub const THOST_FTDC_CSS_Entry: u8 = 49u8;
pub const THOST_FTDC_CSS_Approve: u8 = 50u8;
pub const THOST_FTDC_CSS_Refuse: u8 = 51u8;
pub const THOST_FTDC_CSS_Revoke: u8 = 52u8;
pub const THOST_FTDC_CSS_Send: u8 = 53u8;
pub const THOST_FTDC_CSS_Success: u8 = 54u8;
pub const THOST_FTDC_CSS_Failure: u8 = 55u8;
pub const THOST_FTDC_REQF_NoSend: u8 = 48u8;
pub const THOST_FTDC_REQF_SendSuccess: u8 = 49u8;
pub const THOST_FTDC_REQF_SendFailed: u8 = 50u8;
pub const THOST_FTDC_REQF_WaitReSend: u8 = 51u8;
pub const THOST_FTDC_RESF_Success: u8 = 48u8;
pub const THOST_FTDC_RESF_InsuffiCient: u8 = 49u8;
pub const THOST_FTDC_RESF_UnKnown: u8 = 56u8;
pub const THOST_FTDC_EXS_Before: u8 = 48u8;
pub const THOST_FTDC_EXS_After: u8 = 49u8;
pub const THOST_FTDC_CR_Domestic: u8 = 49u8;
pub const THOST_FTDC_CR_GMT: u8 = 50u8;
pub const THOST_FTDC_CR_Foreign: u8 = 51u8;
pub const THOST_FTDC_HB_No: u8 = 48u8;
pub const THOST_FTDC_HB_Yes: u8 = 49u8;
pub const THOST_FTDC_SM_Normal: u8 = 49u8;
pub const THOST_FTDC_SM_Emerge: u8 = 50u8;
pub const THOST_FTDC_SM_Restore: u8 = 51u8;
pub const THOST_FTDC_TPT_Full: u8 = 49u8;
pub const THOST_FTDC_TPT_Increment: u8 = 50u8;
pub const THOST_FTDC_TPT_BackUp: u8 = 51u8;
pub const THOST_FTDC_LM_Trade: u8 = 48u8;
pub const THOST_FTDC_LM_Transfer: u8 = 49u8;
pub const THOST_FTDC_CPT_Instrument: u8 = 49u8;
pub const THOST_FTDC_CPT_Margin: u8 = 50u8;
pub const THOST_FTDC_HT_Yes: u8 = 49u8;
pub const THOST_FTDC_HT_No: u8 = 48u8;
pub const THOST_FTDC_AMT_Bank: u8 = 49u8;
pub const THOST_FTDC_AMT_Securities: u8 = 50u8;
pub const THOST_FTDC_AMT_Fund: u8 = 51u8;
pub const THOST_FTDC_AMT_Insurance: u8 = 52u8;
pub const THOST_FTDC_AMT_Trust: u8 = 53u8;
pub const THOST_FTDC_AMT_Other: u8 = 57u8;
pub const THOST_FTDC_CFIOT_FundIO: u8 = 48u8;
pub const THOST_FTDC_CFIOT_SwapCurrency: u8 = 49u8;
pub const THOST_FTDC_CAT_Futures: u8 = 49u8;
pub const THOST_FTDC_CAT_AssetmgrFuture: u8 = 50u8;
pub const THOST_FTDC_CAT_AssetmgrTrustee: u8 = 51u8;
pub const THOST_FTDC_CAT_AssetmgrTransfer: u8 = 52u8;
pub const THOST_FTDC_LT_Chinese: u8 = 49u8;
pub const THOST_FTDC_LT_English: u8 = 50u8;
pub const THOST_FTDC_AMCT_Person: u8 = 49u8;
pub const THOST_FTDC_AMCT_Organ: u8 = 50u8;
pub const THOST_FTDC_AMCT_SpecialOrgan: u8 = 52u8;
pub const THOST_FTDC_ASST_Futures: u8 = 51u8;
pub const THOST_FTDC_ASST_SpecialOrgan: u8 = 52u8;
pub const THOST_FTDC_CIT_HasExch: u8 = 48u8;
pub const THOST_FTDC_CIT_HasATP: u8 = 49u8;
pub const THOST_FTDC_CIT_HasDiff: u8 = 50u8;
pub const THOST_FTDC_DT_HandDeliv: u8 = 49u8;
pub const THOST_FTDC_DT_PersonDeliv: u8 = 50u8;
pub const THOST_FTDC_MMSA_NO: u8 = 48u8;
pub const THOST_FTDC_MMSA_YES: u8 = 49u8;
pub const THOST_FTDC_CACT_Person: u8 = 48u8;
pub const THOST_FTDC_CACT_Company: u8 = 49u8;
pub const THOST_FTDC_CACT_Other: u8 = 50u8;
pub const THOST_FTDC_UOAAT_Futures: u8 = 49u8;
pub const THOST_FTDC_UOAAT_SpecialOrgan: u8 = 50u8;
pub const THOST_FTDC_DEN_Buy: u8 = 48u8;
pub const THOST_FTDC_DEN_Sell: u8 = 49u8;
pub const THOST_FTDC_OFEN_Open: u8 = 48u8;
pub const THOST_FTDC_OFEN_Close: u8 = 49u8;
pub const THOST_FTDC_OFEN_ForceClose: u8 = 50u8;
pub const THOST_FTDC_OFEN_CloseToday: u8 = 51u8;
pub const THOST_FTDC_OFEN_CloseYesterday: u8 = 52u8;
pub const THOST_FTDC_OFEN_ForceOff: u8 = 53u8;
pub const THOST_FTDC_OFEN_LocalForceClose: u8 = 54u8;
pub const THOST_FTDC_HFEN_Speculation: u8 = 49u8;
pub const THOST_FTDC_HFEN_Arbitrage: u8 = 50u8;
pub const THOST_FTDC_HFEN_Hedge: u8 = 51u8;
pub const THOST_FTDC_FIOTEN_FundIO: u8 = 49u8;
pub const THOST_FTDC_FIOTEN_Transfer: u8 = 50u8;
pub const THOST_FTDC_FIOTEN_SwapCurrency: u8 = 51u8;
pub const THOST_FTDC_FTEN_Deposite: u8 = 49u8;
pub const THOST_FTDC_FTEN_ItemFund: u8 = 50u8;
pub const THOST_FTDC_FTEN_Company: u8 = 51u8;
pub const THOST_FTDC_FTEN_InnerTransfer: u8 = 52u8;
pub const THOST_FTDC_FDEN_In: u8 = 49u8;
pub const THOST_FTDC_FDEN_Out: u8 = 50u8;
pub const THOST_FTDC_FMDEN_In: u8 = 49u8;
pub const THOST_FTDC_FMDEN_Out: u8 = 50u8;
pub const THOST_FTDC_CP_CallOptions: u8 = 49u8;
pub const THOST_FTDC_CP_PutOptions: u8 = 50u8;
pub const THOST_FTDC_STM_Continental: u8 = 48u8;
pub const THOST_FTDC_STM_American: u8 = 49u8;
pub const THOST_FTDC_STM_Bermuda: u8 = 50u8;
pub const THOST_FTDC_STT_Hedge: u8 = 48u8;
pub const THOST_FTDC_STT_Match: u8 = 49u8;
pub const THOST_FTDC_APPT_NotStrikeNum: u8 = 52u8;
pub const THOST_FTDC_GUDS_Gen: u8 = 48u8;
pub const THOST_FTDC_GUDS_Hand: u8 = 49u8;
pub const THOST_FTDC_OER_NoExec: u8 = 110u8;
pub const THOST_FTDC_OER_Canceled: u8 = 99u8;
pub const THOST_FTDC_OER_OK: u8 = 48u8;
pub const THOST_FTDC_OER_NoPosition: u8 = 49u8;
pub const THOST_FTDC_OER_NoDeposit: u8 = 50u8;
pub const THOST_FTDC_OER_NoParticipant: u8 = 51u8;
pub const THOST_FTDC_OER_NoClient: u8 = 52u8;
pub const THOST_FTDC_OER_NoInstrument: u8 = 54u8;
pub const THOST_FTDC_OER_NoRight: u8 = 55u8;
pub const THOST_FTDC_OER_InvalidVolume: u8 = 56u8;
pub const THOST_FTDC_OER_NoEnoughHistoryTrade: u8 = 57u8;
pub const THOST_FTDC_OER_Unknown: u8 = 97u8;
pub const THOST_FTDC_COMBT_Future: u8 = 48u8;
pub const THOST_FTDC_COMBT_BUL: u8 = 49u8;
pub const THOST_FTDC_COMBT_BER: u8 = 50u8;
pub const THOST_FTDC_COMBT_STD: u8 = 51u8;
pub const THOST_FTDC_COMBT_STG: u8 = 52u8;
pub const THOST_FTDC_COMBT_PRT: u8 = 53u8;
pub const THOST_FTDC_COMBT_CAS: u8 = 54u8;
pub const THOST_FTDC_COMBT_OPL: u8 = 55u8;
pub const THOST_FTDC_COMBT_BFO: u8 = 56u8;
pub const THOST_FTDC_COMBT_BLS: u8 = 57u8;
pub const THOST_FTDC_COMBT_BES: u8 = 97u8;
pub const THOST_FTDC_DCECOMBT_SPL: u8 = 48u8;
pub const THOST_FTDC_DCECOMBT_OPL: u8 = 49u8;
pub const THOST_FTDC_DCECOMBT_SP: u8 = 50u8;
pub const THOST_FTDC_DCECOMBT_SPC: u8 = 51u8;
pub const THOST_FTDC_DCECOMBT_BLS: u8 = 52u8;
pub const THOST_FTDC_DCECOMBT_BES: u8 = 53u8;
pub const THOST_FTDC_DCECOMBT_CAS: u8 = 54u8;
pub const THOST_FTDC_DCECOMBT_STD: u8 = 55u8;
pub const THOST_FTDC_DCECOMBT_STG: u8 = 56u8;
pub const THOST_FTDC_DCECOMBT_BFO: u8 = 57u8;
pub const THOST_FTDC_DCECOMBT_SFO: u8 = 97u8;
pub const THOST_FTDC_ORPT_PreSettlementPrice: u8 = 49u8;
pub const THOST_FTDC_ORPT_OpenPrice: u8 = 52u8;
pub const THOST_FTDC_ORPT_MaxPreSettlementPrice: u8 = 53u8;
pub const THOST_FTDC_BLAG_Default: u8 = 49u8;
pub const THOST_FTDC_BLAG_IncludeOptValLost: u8 = 50u8;
pub const THOST_FTDC_ACTP_Exec: u8 = 49u8;
pub const THOST_FTDC_ACTP_Abandon: u8 = 50u8;
pub const THOST_FTDC_FQST_Submitted: u8 = 97u8;
pub const THOST_FTDC_FQST_Accepted: u8 = 98u8;
pub const THOST_FTDC_FQST_Rejected: u8 = 99u8;
pub const THOST_FTDC_VM_Absolute: u8 = 48u8;
pub const THOST_FTDC_VM_Ratio: u8 = 49u8;
pub const THOST_FTDC_EOPF_Reserve: u8 = 48u8;
pub const THOST_FTDC_EOPF_UnReserve: u8 = 49u8;
pub const THOST_FTDC_EOCF_AutoClose: u8 = 48u8;
pub const THOST_FTDC_EOCF_NotToClose: u8 = 49u8;
pub const THOST_FTDC_PTE_Futures: u8 = 49u8;
pub const THOST_FTDC_PTE_Options: u8 = 50u8;
pub const THOST_FTDC_CUFN_CUFN_O: u8 = 79u8;
pub const THOST_FTDC_CUFN_CUFN_T: u8 = 84u8;
pub const THOST_FTDC_CUFN_CUFN_P: u8 = 80u8;
pub const THOST_FTDC_CUFN_CUFN_N: u8 = 78u8;
pub const THOST_FTDC_CUFN_CUFN_L: u8 = 76u8;
pub const THOST_FTDC_CUFN_CUFN_F: u8 = 70u8;
pub const THOST_FTDC_CUFN_CUFN_C: u8 = 67u8;
pub const THOST_FTDC_CUFN_CUFN_M: u8 = 77u8;
pub const THOST_FTDC_DUFN_DUFN_O: u8 = 79u8;
pub const THOST_FTDC_DUFN_DUFN_T: u8 = 84u8;
pub const THOST_FTDC_DUFN_DUFN_P: u8 = 80u8;
pub const THOST_FTDC_DUFN_DUFN_F: u8 = 70u8;
pub const THOST_FTDC_DUFN_DUFN_C: u8 = 67u8;
pub const THOST_FTDC_DUFN_DUFN_D: u8 = 68u8;
pub const THOST_FTDC_DUFN_DUFN_M: u8 = 77u8;
pub const THOST_FTDC_DUFN_DUFN_S: u8 = 83u8;
pub const THOST_FTDC_SUFN_SUFN_O: u8 = 79u8;
pub const THOST_FTDC_SUFN_SUFN_T: u8 = 84u8;
pub const THOST_FTDC_SUFN_SUFN_P: u8 = 80u8;
pub const THOST_FTDC_SUFN_SUFN_F: u8 = 70u8;
pub const THOST_FTDC_CFUFN_SUFN_T: u8 = 84u8;
pub const THOST_FTDC_CFUFN_SUFN_P: u8 = 80u8;
pub const THOST_FTDC_CFUFN_SUFN_F: u8 = 70u8;
pub const THOST_FTDC_CFUFN_SUFN_S: u8 = 83u8;
pub const THOST_FTDC_CMDR_Comb: u8 = 48u8;
pub const THOST_FTDC_CMDR_UnComb: u8 = 49u8;
pub const THOST_FTDC_CMDR_DelComb: u8 = 50u8;
pub const THOST_FTDC_STOV_RealValue: u8 = 49u8;
pub const THOST_FTDC_STOV_ProfitValue: u8 = 50u8;
pub const THOST_FTDC_STOV_RealRatio: u8 = 51u8;
pub const THOST_FTDC_STOV_ProfitRatio: u8 = 52u8;
pub const THOST_FTDC_ROAST_Processing: u8 = 48u8;
pub const THOST_FTDC_ROAST_Cancelled: u8 = 49u8;
pub const THOST_FTDC_ROAST_Opened: u8 = 50u8;
pub const THOST_FTDC_ROAST_Invalid: u8 = 51u8;
pub const THOST_FTDC_WPSR_Lib: u8 = 49u8;
pub const THOST_FTDC_WPSR_Manual: u8 = 50u8;
pub const THOST_FTDC_OSCF_CloseSelfOptionPosition: u8 = 49u8;
pub const THOST_FTDC_OSCF_ReserveOptionPosition: u8 = 50u8;
pub const THOST_FTDC_OSCF_SellCloseSelfFuturePosition: u8 = 51u8;
pub const THOST_FTDC_OSCF_ReserveFuturePosition: u8 = 52u8;
pub const THOST_FTDC_BZTP_Future: u8 = 49u8;
pub const THOST_FTDC_BZTP_Stock: u8 = 50u8;
pub const THOST_FTDC_APP_TYPE_Investor: u8 = 49u8;
pub const THOST_FTDC_APP_TYPE_InvestorRelay: u8 = 50u8;
pub const THOST_FTDC_APP_TYPE_OperatorRelay: u8 = 51u8;
pub const THOST_FTDC_APP_TYPE_UnKnown: u8 = 52u8;
pub const THOST_FTDC_RV_Right: u8 = 48u8;
pub const THOST_FTDC_RV_Refuse: u8 = 49u8;
pub const THOST_FTDC_OTC_TRDT_Block: u8 = 48u8;
pub const THOST_FTDC_OTC_TRDT_EFP: u8 = 49u8;
pub const THOST_FTDC_OTC_MT_DV01: u8 = 49u8;
pub const THOST_FTDC_OTC_MT_ParValue: u8 = 50u8;
pub const THOST_FTDC_AU_WHITE: u8 = 48u8;
pub const THOST_FTDC_AU_BLACK: u8 = 49u8;
pub const THOST_FTDC_INS_ALL: u8 = 48u8;
pub const THOST_FTDC_INS_FUTURE: u8 = 49u8;
pub const THOST_FTDC_INS_OPTION: u8 = 50u8;
pub const THOST_FTDC_INS_COMB: u8 = 51u8;
pub const THOST_FTDC_TD_ALL: u8 = 48u8;
pub const THOST_FTDC_TD_TRADE: u8 = 49u8;
pub const THOST_FTDC_TD_UNTRADE: u8 = 50u8;
pub const THOST_FTDC_PS_tradeable: u8 = 49u8;
pub const THOST_FTDC_PS_untradeable: u8 = 50u8;
pub const THOST_FTDC_SDS_Readable: u8 = 49u8;
pub const THOST_FTDC_SDS_Reading: u8 = 50u8;
pub const THOST_FTDC_SDS_Readend: u8 = 51u8;
pub const THOST_FTDC_SDS_OptErr: u8 = 101u8;
pub const THOST_FTDC_ACD_Add: u8 = 49u8;
pub const THOST_FTDC_ACD_Del: u8 = 50u8;
pub const THOST_FTDC_ACD_Upd: u8 = 51u8;
pub const THOST_FTDC_OAC_Balance: u8 = 49u8;
pub const THOST_FTDC_OAC_OrigFirst: u8 = 50u8;
pub const THOST_TE_RESUME_TYPE_THOST_TERT_RESTART: THOST_TE_RESUME_TYPE = 0;
pub const THOST_TE_RESUME_TYPE_THOST_TERT_RESUME: THOST_TE_RESUME_TYPE = 1;
pub const THOST_TE_RESUME_TYPE_THOST_TERT_QUICK: THOST_TE_RESUME_TYPE = 2;
pub const THOST_TE_RESUME_TYPE_THOST_TERT_NONE: THOST_TE_RESUME_TYPE = 3;
pub type THOST_TE_RESUME_TYPE = ::std::os::raw::c_int;
pub type TThostFtdcTraderIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcInvestorIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcBrokerIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcBrokerAbbrType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcBrokerNameType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcOldExchangeInstIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcExchangeInstIDType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcOrderRefType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcParticipantIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcUserIDType = [::std::os::raw::c_char; 16usize];
pub type TThostFtdcPasswordType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcClientIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcInstrumentIDType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcOldInstrumentIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcInstrumentCodeType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcMarketIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcProductNameType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcExchangeIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcExchangeNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcExchangeAbbrType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcExchangeFlagType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcMacAddressType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcSystemIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcClientLoginRemarkType = [::std::os::raw::c_char; 151usize];
pub type TThostFtdcExchangePropertyType = ::std::os::raw::c_char;
pub type TThostFtdcDateType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcTimeType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcLongTimeType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcInstrumentNameType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcSettlementGroupIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcOrderSysIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcTradeIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcCommandTypeType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcOldIPAddressType = [::std::os::raw::c_char; 16usize];
pub type TThostFtdcIPAddressType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcIPPortType = ::std::os::raw::c_int;
pub type TThostFtdcProductInfoType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcProtocolInfoType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcBusinessUnitType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcDepositSeqNoType = [::std::os::raw::c_char; 15usize];
pub type TThostFtdcIdentifiedCardNoType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcIdCardTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOrderLocalIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcUserNameType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcPartyNameType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcErrorMsgType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcFieldNameType = [::std::os::raw::c_char; 2049usize];
pub type TThostFtdcFieldContentType = [::std::os::raw::c_char; 2049usize];
pub type TThostFtdcSystemNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcContentType = [::std::os::raw::c_char; 501usize];
pub type TThostFtdcInvestorRangeType = ::std::os::raw::c_char;
pub type TThostFtdcDepartmentRangeType = ::std::os::raw::c_char;
pub type TThostFtdcDataSyncStatusType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerDataSyncStatusType = ::std::os::raw::c_char;
pub type TThostFtdcExchangeConnectStatusType = ::std::os::raw::c_char;
pub type TThostFtdcTraderConnectStatusType = ::std::os::raw::c_char;
pub type TThostFtdcFunctionCodeType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerFunctionCodeType = ::std::os::raw::c_char;
pub type TThostFtdcOrderActionStatusType = ::std::os::raw::c_char;
pub type TThostFtdcOrderStatusType = ::std::os::raw::c_char;
pub type TThostFtdcOrderSubmitStatusType = ::std::os::raw::c_char;
pub type TThostFtdcPositionDateType = ::std::os::raw::c_char;
pub type TThostFtdcPositionDateTypeType = ::std::os::raw::c_char;
pub type TThostFtdcTradingRoleType = ::std::os::raw::c_char;
pub type TThostFtdcProductClassType = ::std::os::raw::c_char;
pub type TThostFtdcAPIProductClassType = ::std::os::raw::c_char;
pub type TThostFtdcInstLifePhaseType = ::std::os::raw::c_char;
pub type TThostFtdcDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcPositionTypeType = ::std::os::raw::c_char;
pub type TThostFtdcPosiDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcSysSettlementStatusType = ::std::os::raw::c_char;
pub type TThostFtdcRatioAttrType = ::std::os::raw::c_char;
pub type TThostFtdcHedgeFlagType = ::std::os::raw::c_char;
pub type TThostFtdcBillHedgeFlagType = ::std::os::raw::c_char;
pub type TThostFtdcClientIDTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOrderPriceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOffsetFlagType = ::std::os::raw::c_char;
pub type TThostFtdcForceCloseReasonType = ::std::os::raw::c_char;
pub type TThostFtdcOrderTypeType = ::std::os::raw::c_char;
pub type TThostFtdcTimeConditionType = ::std::os::raw::c_char;
pub type TThostFtdcVolumeConditionType = ::std::os::raw::c_char;
pub type TThostFtdcContingentConditionType = ::std::os::raw::c_char;
pub type TThostFtdcActionFlagType = ::std::os::raw::c_char;
pub type TThostFtdcTradingRightType = ::std::os::raw::c_char;
pub type TThostFtdcOrderSourceType = ::std::os::raw::c_char;
pub type TThostFtdcTradeTypeType = ::std::os::raw::c_char;
pub type TThostFtdcSpecPosiTypeType = ::std::os::raw::c_char;
pub type TThostFtdcPriceSourceType = ::std::os::raw::c_char;
pub type TThostFtdcInstrumentStatusType = ::std::os::raw::c_char;
pub type TThostFtdcInstStatusEnterReasonType = ::std::os::raw::c_char;
pub type TThostFtdcOrderActionRefType = ::std::os::raw::c_int;
pub type TThostFtdcInstallCountType = ::std::os::raw::c_int;
pub type TThostFtdcInstallIDType = ::std::os::raw::c_int;
pub type TThostFtdcErrorIDType = ::std::os::raw::c_int;
pub type TThostFtdcSettlementIDType = ::std::os::raw::c_int;
pub type TThostFtdcVolumeType = ::std::os::raw::c_int;
pub type TThostFtdcFrontIDType = ::std::os::raw::c_int;
pub type TThostFtdcSessionIDType = ::std::os::raw::c_int;
pub type TThostFtdcSequenceNoType = ::std::os::raw::c_int;
pub type TThostFtdcCommandNoType = ::std::os::raw::c_int;
pub type TThostFtdcMillisecType = ::std::os::raw::c_int;
pub type TThostFtdcSecType = ::std::os::raw::c_int;
pub type TThostFtdcVolumeMultipleType = ::std::os::raw::c_int;
pub type TThostFtdcTradingSegmentSNType = ::std::os::raw::c_int;
pub type TThostFtdcRequestIDType = ::std::os::raw::c_int;
pub type TThostFtdcYearType = ::std::os::raw::c_int;
pub type TThostFtdcMonthType = ::std::os::raw::c_int;
pub type TThostFtdcBoolType = ::std::os::raw::c_int;
pub type TThostFtdcPriceType = f64;
pub type TThostFtdcCombOffsetFlagType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcCombHedgeFlagType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcRatioType = f64;
pub type TThostFtdcMoneyType = f64;
pub type TThostFtdcLargeVolumeType = f64;
pub type TThostFtdcSequenceSeriesType = ::std::os::raw::c_short;
pub type TThostFtdcCommPhaseNoType = ::std::os::raw::c_short;
pub type TThostFtdcSequenceLabelType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcUnderlyingMultipleType = f64;
pub type TThostFtdcPriorityType = ::std::os::raw::c_int;
pub type TThostFtdcContractCodeType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcCityType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcIsStockType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcChannelType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcAddressType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcZipCodeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcTelephoneType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcFaxType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcMobileType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcEMailType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcMemoType = [::std::os::raw::c_char; 161usize];
pub type TThostFtdcCompanyCodeType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcWebsiteType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcTaxNoType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcBatchStatusType = ::std::os::raw::c_char;
pub type TThostFtdcPropertyIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcPropertyNameType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcLicenseNoType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcAgentIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcAgentNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcAgentGroupIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcAgentGroupNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcReturnStyleType = ::std::os::raw::c_char;
pub type TThostFtdcReturnPatternType = ::std::os::raw::c_char;
pub type TThostFtdcReturnLevelType = ::std::os::raw::c_char;
pub type TThostFtdcReturnStandardType = ::std::os::raw::c_char;
pub type TThostFtdcMortgageTypeType = ::std::os::raw::c_char;
pub type TThostFtdcInvestorSettlementParamIDType = ::std::os::raw::c_char;
pub type TThostFtdcExchangeSettlementParamIDType = ::std::os::raw::c_char;
pub type TThostFtdcSystemParamIDType = ::std::os::raw::c_char;
pub type TThostFtdcTradeParamIDType = ::std::os::raw::c_char;
pub type TThostFtdcSettlementParamValueType = [::std::os::raw::c_char; 256usize];
pub type TThostFtdcCounterIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcInvestorGroupNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcBrandCodeType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcWarehouseType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcProductDateType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcGradeType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcClassifyType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcPositionType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcYieldlyType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcWeightType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcSubEntryFundNoType = ::std::os::raw::c_int;
pub type TThostFtdcFileIDType = ::std::os::raw::c_char;
pub type TThostFtdcFileNameType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcFileTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFileFormatType = ::std::os::raw::c_char;
pub type TThostFtdcFileUploadStatusType = ::std::os::raw::c_char;
pub type TThostFtdcTransferDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcUploadModeType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcAccountIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcBankFlagType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcBankAccountType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcOpenNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcOpenBankType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcBankNameType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcPublishPathType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcOperatorIDType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcMonthCountType = ::std::os::raw::c_int;
pub type TThostFtdcAdvanceMonthArrayType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcDateExprType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcInstrumentIDExprType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcInstrumentNameExprType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcSpecialCreateRuleType = ::std::os::raw::c_char;
pub type TThostFtdcBasisPriceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcProductLifePhaseType = ::std::os::raw::c_char;
pub type TThostFtdcDeliveryModeType = ::std::os::raw::c_char;
pub type TThostFtdcLogLevelType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcProcessNameType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcOperationMemoType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcFundIOTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFundTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFundDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcFundStatusType = ::std::os::raw::c_char;
pub type TThostFtdcBillNoType = [::std::os::raw::c_char; 15usize];
pub type TThostFtdcBillNameType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcPublishStatusType = ::std::os::raw::c_char;
pub type TThostFtdcEnumValueIDType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcEnumValueTypeType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcEnumValueLabelType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcEnumValueResultType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcSystemStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSettlementStatusType = ::std::os::raw::c_char;
pub type TThostFtdcRangeIntTypeType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcRangeIntFromType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcRangeIntToType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcFunctionIDType = [::std::os::raw::c_char; 25usize];
pub type TThostFtdcFunctionValueCodeType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcFunctionNameType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcRoleIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcRoleNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcDescriptionType = [::std::os::raw::c_char; 401usize];
pub type TThostFtdcCombineIDType = [::std::os::raw::c_char; 25usize];
pub type TThostFtdcCombineTypeType = [::std::os::raw::c_char; 25usize];
pub type TThostFtdcInvestorTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerTypeType = ::std::os::raw::c_char;
pub type TThostFtdcRiskLevelType = ::std::os::raw::c_char;
pub type TThostFtdcFeeAcceptStyleType = ::std::os::raw::c_char;
pub type TThostFtdcPasswordTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAlgorithmType = ::std::os::raw::c_char;
pub type TThostFtdcIncludeCloseProfitType = ::std::os::raw::c_char;
pub type TThostFtdcAllWithoutTradeType = ::std::os::raw::c_char;
pub type TThostFtdcCommentType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcVersionType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcTradeCodeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcTradeDateType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcTradeTimeType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcTradeSerialType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcTradeSerialNoType = ::std::os::raw::c_int;
pub type TThostFtdcFutureIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcBankIDType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcBankBrchIDType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcBankBranchIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcOperNoType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcDeviceIDType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcRecordNumType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcFutureAccountType = [::std::os::raw::c_char; 22usize];
pub type TThostFtdcFuturePwdFlagType = ::std::os::raw::c_char;
pub type TThostFtdcTransferTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFutureAccPwdType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcCurrencyCodeType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcRetCodeType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcRetInfoType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcTradeAmtType = [::std::os::raw::c_char; 20usize];
pub type TThostFtdcUseAmtType = [::std::os::raw::c_char; 20usize];
pub type TThostFtdcFetchAmtType = [::std::os::raw::c_char; 20usize];
pub type TThostFtdcTransferValidFlagType = ::std::os::raw::c_char;
pub type TThostFtdcCertCodeType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcReasonType = ::std::os::raw::c_char;
pub type TThostFtdcFundProjectIDType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcSexType = ::std::os::raw::c_char;
pub type TThostFtdcProfessionType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcNationalType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcProvinceType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcRegionType = [::std::os::raw::c_char; 16usize];
pub type TThostFtdcCountryType = [::std::os::raw::c_char; 16usize];
pub type TThostFtdcLicenseNOType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcCompanyTypeType = [::std::os::raw::c_char; 16usize];
pub type TThostFtdcBusinessScopeType = [::std::os::raw::c_char; 1001usize];
pub type TThostFtdcCapitalCurrencyType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcUserTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBranchIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcRateTypeType = ::std::os::raw::c_char;
pub type TThostFtdcNoteTypeType = ::std::os::raw::c_char;
pub type TThostFtdcSettlementStyleType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerDNSType = [::std::os::raw::c_char; 256usize];
pub type TThostFtdcSentenceType = [::std::os::raw::c_char; 501usize];
pub type TThostFtdcSettlementBillTypeType = ::std::os::raw::c_char;
pub type TThostFtdcUserRightTypeType = ::std::os::raw::c_char;
pub type TThostFtdcMarginPriceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBillGenStatusType = ::std::os::raw::c_char;
pub type TThostFtdcAlgoTypeType = ::std::os::raw::c_char;
pub type TThostFtdcHandlePositionAlgoIDType = ::std::os::raw::c_char;
pub type TThostFtdcFindMarginRateAlgoIDType = ::std::os::raw::c_char;
pub type TThostFtdcHandleTradingAccountAlgoIDType = ::std::os::raw::c_char;
pub type TThostFtdcPersonTypeType = ::std::os::raw::c_char;
pub type TThostFtdcQueryInvestorRangeType = ::std::os::raw::c_char;
pub type TThostFtdcInvestorRiskStatusType = ::std::os::raw::c_char;
pub type TThostFtdcLegIDType = ::std::os::raw::c_int;
pub type TThostFtdcLegMultipleType = ::std::os::raw::c_int;
pub type TThostFtdcImplyLevelType = ::std::os::raw::c_int;
pub type TThostFtdcClearAccountType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcOrganNOType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcClearbarchIDType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcUserEventTypeType = ::std::os::raw::c_char;
pub type TThostFtdcUserEventInfoType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcCloseStyleType = ::std::os::raw::c_char;
pub type TThostFtdcStatModeType = ::std::os::raw::c_char;
pub type TThostFtdcParkedOrderStatusType = ::std::os::raw::c_char;
pub type TThostFtdcParkedOrderIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcParkedOrderActionIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcVirDealStatusType = ::std::os::raw::c_char;
pub type TThostFtdcOrgSystemIDType = ::std::os::raw::c_char;
pub type TThostFtdcVirTradeStatusType = ::std::os::raw::c_char;
pub type TThostFtdcVirBankAccTypeType = ::std::os::raw::c_char;
pub type TThostFtdcVirementStatusType = ::std::os::raw::c_char;
pub type TThostFtdcVirementAvailAbilityType = ::std::os::raw::c_char;
pub type TThostFtdcVirementTradeCodeType = ::std::os::raw::c_char;
pub type TThostFtdcPhotoTypeNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcPhotoTypeIDType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcPhotoNameType = [::std::os::raw::c_char; 161usize];
pub type TThostFtdcTopicIDType = ::std::os::raw::c_int;
pub type TThostFtdcReportTypeIDType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcCharacterIDType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcAMLParamIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcAMLInvestorTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLIdCardTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLTradeDirectType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLTradeModelType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLOpParamValueType = f64;
pub type TThostFtdcAMLCustomerCardTypeType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcAMLInstitutionNameType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcAMLDistrictIDType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcAMLRelationShipType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLInstitutionTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLInstitutionIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcAMLAccountTypeType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcAMLTradingTypeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcAMLTransactClassType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcAMLCapitalIOType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcAMLSiteType = [::std::os::raw::c_char; 10usize];
pub type TThostFtdcAMLCapitalPurposeType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcAMLReportTypeType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcAMLSerialNoType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcAMLStatusType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcAMLGenStatusType = ::std::os::raw::c_char;
pub type TThostFtdcAMLSeqCodeType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcAMLFileNameType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcAMLMoneyType = f64;
pub type TThostFtdcAMLFileAmountType = ::std::os::raw::c_int;
pub type TThostFtdcCFMMCKeyType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcCFMMCTokenType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcCFMMCKeyKindType = ::std::os::raw::c_char;
pub type TThostFtdcAMLReportNameType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcIndividualNameType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcCurrencyIDType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcCustNumberType = [::std::os::raw::c_char; 36usize];
pub type TThostFtdcOrganCodeType = [::std::os::raw::c_char; 36usize];
pub type TThostFtdcOrganNameType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcSuperOrganCodeType = [::std::os::raw::c_char; 12usize];
pub type TThostFtdcSubBranchIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcSubBranchNameType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcBranchNetCodeType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcBranchNetNameType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcOrganFlagType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcBankCodingForFutureType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcBankReturnCodeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcPlateReturnCodeType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcBankSubBranchIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcFutureBranchIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcReturnCodeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcOperatorCodeType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcClearDepIDType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcClearBrchIDType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcClearNameType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcBankAccountNameType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcInvDepIDType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcInvBrchIDType = [::std::os::raw::c_char; 6usize];
pub type TThostFtdcMessageFormatVersionType = [::std::os::raw::c_char; 36usize];
pub type TThostFtdcDigestType = [::std::os::raw::c_char; 36usize];
pub type TThostFtdcAuthenticDataType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcPasswordKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcFutureAccountNameType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcMobilePhoneType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcFutureMainKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcFutureWorkKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcFutureTransKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcBankMainKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcBankWorkKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcBankTransKeyType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcBankServerDescriptionType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcAddInfoType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcDescrInfoForReturnCodeType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcCountryCodeType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcSerialType = ::std::os::raw::c_int;
pub type TThostFtdcPlateSerialType = ::std::os::raw::c_int;
pub type TThostFtdcBankSerialType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcCorrectSerialType = ::std::os::raw::c_int;
pub type TThostFtdcFutureSerialType = ::std::os::raw::c_int;
pub type TThostFtdcApplicationIDType = ::std::os::raw::c_int;
pub type TThostFtdcBankProxyIDType = ::std::os::raw::c_int;
pub type TThostFtdcFBTCoreIDType = ::std::os::raw::c_int;
pub type TThostFtdcServerPortType = ::std::os::raw::c_int;
pub type TThostFtdcRepealedTimesType = ::std::os::raw::c_int;
pub type TThostFtdcRepealTimeIntervalType = ::std::os::raw::c_int;
pub type TThostFtdcTotalTimesType = ::std::os::raw::c_int;
pub type TThostFtdcFBTRequestIDType = ::std::os::raw::c_int;
pub type TThostFtdcTIDType = ::std::os::raw::c_int;
pub type TThostFtdcTradeAmountType = f64;
pub type TThostFtdcCustFeeType = f64;
pub type TThostFtdcFutureFeeType = f64;
pub type TThostFtdcSingleMaxAmtType = f64;
pub type TThostFtdcSingleMinAmtType = f64;
pub type TThostFtdcTotalAmtType = f64;
pub type TThostFtdcCertificationTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFileBusinessCodeType = ::std::os::raw::c_char;
pub type TThostFtdcCashExchangeCodeType = ::std::os::raw::c_char;
pub type TThostFtdcYesNoIndicatorType = ::std::os::raw::c_char;
pub type TThostFtdcBanlanceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcGenderType = ::std::os::raw::c_char;
pub type TThostFtdcFeePayFlagType = ::std::os::raw::c_char;
pub type TThostFtdcPassWordKeyTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBTPassWordTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBTEncryModeType = ::std::os::raw::c_char;
pub type TThostFtdcBankRepealFlagType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerRepealFlagType = ::std::os::raw::c_char;
pub type TThostFtdcInstitutionTypeType = ::std::os::raw::c_char;
pub type TThostFtdcLastFragmentType = ::std::os::raw::c_char;
pub type TThostFtdcBankAccStatusType = ::std::os::raw::c_char;
pub type TThostFtdcMoneyAccountStatusType = ::std::os::raw::c_char;
pub type TThostFtdcManageStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSystemTypeType = ::std::os::raw::c_char;
pub type TThostFtdcTxnEndFlagType = ::std::os::raw::c_char;
pub type TThostFtdcProcessStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCustTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBTTransferDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcOpenOrDestroyType = ::std::os::raw::c_char;
pub type TThostFtdcAvailabilityFlagType = ::std::os::raw::c_char;
pub type TThostFtdcOrganTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOrganLevelType = ::std::os::raw::c_char;
pub type TThostFtdcProtocalIDType = ::std::os::raw::c_char;
pub type TThostFtdcConnectModeType = ::std::os::raw::c_char;
pub type TThostFtdcSyncModeType = ::std::os::raw::c_char;
pub type TThostFtdcBankAccTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFutureAccTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOrganStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCCBFeeModeType = ::std::os::raw::c_char;
pub type TThostFtdcCommApiTypeType = ::std::os::raw::c_char;
pub type TThostFtdcServiceIDType = ::std::os::raw::c_int;
pub type TThostFtdcServiceLineNoType = ::std::os::raw::c_int;
pub type TThostFtdcServiceNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcLinkStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCommApiPointerType = ::std::os::raw::c_int;
pub type TThostFtdcPwdFlagType = ::std::os::raw::c_char;
pub type TThostFtdcSecuAccTypeType = ::std::os::raw::c_char;
pub type TThostFtdcTransferStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSponsorTypeType = ::std::os::raw::c_char;
pub type TThostFtdcReqRspTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBTUserEventTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBankIDByBankType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcBankOperNoType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcBankCustNoType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcDBOPSeqNoType = ::std::os::raw::c_int;
pub type TThostFtdcTableNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcPKNameType = [::std::os::raw::c_char; 201usize];
pub type TThostFtdcPKValueType = [::std::os::raw::c_char; 501usize];
pub type TThostFtdcDBOperationType = ::std::os::raw::c_char;
pub type TThostFtdcSyncFlagType = ::std::os::raw::c_char;
pub type TThostFtdcTargetIDType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcSyncTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBETimeType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcFBEBankNoType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcFBECertNoType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcExDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcFBEBankAccountType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcFBEBankAccountNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcFBEAmtType = f64;
pub type TThostFtdcFBEBusinessTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcFBEPostScriptType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcFBERemarkType = [::std::os::raw::c_char; 71usize];
pub type TThostFtdcExRateType = f64;
pub type TThostFtdcFBEResultFlagType = ::std::os::raw::c_char;
pub type TThostFtdcFBERtnMsgType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcFBEExtendMsgType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcFBEBusinessSerialType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcFBESystemSerialType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcFBETotalExCntType = ::std::os::raw::c_int;
pub type TThostFtdcFBEExchStatusType = ::std::os::raw::c_char;
pub type TThostFtdcFBEFileFlagType = ::std::os::raw::c_char;
pub type TThostFtdcFBEAlreadyTradeType = ::std::os::raw::c_char;
pub type TThostFtdcFBEOpenBankType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcFBEUserEventTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFBEFileNameType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcFBEBatchSerialType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcFBEReqFlagType = ::std::os::raw::c_char;
pub type TThostFtdcNotifyClassType = ::std::os::raw::c_char;
pub type TThostFtdcRiskNofityInfoType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcForceCloseSceneIdType = [::std::os::raw::c_char; 24usize];
pub type TThostFtdcForceCloseTypeType = ::std::os::raw::c_char;
pub type TThostFtdcInstrumentIDsType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcRiskNotifyMethodType = ::std::os::raw::c_char;
pub type TThostFtdcRiskNotifyStatusType = ::std::os::raw::c_char;
pub type TThostFtdcRiskUserEventType = ::std::os::raw::c_char;
pub type TThostFtdcParamIDType = ::std::os::raw::c_int;
pub type TThostFtdcParamNameType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcParamValueType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcConditionalOrderSortTypeType = ::std::os::raw::c_char;
pub type TThostFtdcSendTypeType = ::std::os::raw::c_char;
pub type TThostFtdcClientIDStatusType = ::std::os::raw::c_char;
pub type TThostFtdcIndustryIDType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcQuestionIDType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcQuestionContentType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcOptionIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcOptionContentType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcQuestionTypeType = ::std::os::raw::c_char;
pub type TThostFtdcProcessIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcSeqNoType = ::std::os::raw::c_int;
pub type TThostFtdcUOAProcessStatusType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcProcessTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcBusinessTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCfmmcReturnCodeType = ::std::os::raw::c_char;
pub type TThostFtdcExReturnCodeType = ::std::os::raw::c_int;
pub type TThostFtdcClientTypeType = ::std::os::raw::c_char;
pub type TThostFtdcExchangeIDTypeType = ::std::os::raw::c_char;
pub type TThostFtdcExClientIDTypeType = ::std::os::raw::c_char;
pub type TThostFtdcClientClassifyType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcUOAOrganTypeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcUOACountryCodeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcAreaCodeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcFuturesIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcCffmcDateType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcCffmcTimeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcNocIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcUpdateFlagType = ::std::os::raw::c_char;
pub type TThostFtdcApplyOperateIDType = ::std::os::raw::c_char;
pub type TThostFtdcApplyStatusIDType = ::std::os::raw::c_char;
pub type TThostFtdcSendMethodType = ::std::os::raw::c_char;
pub type TThostFtdcEventTypeType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcEventModeType = ::std::os::raw::c_char;
pub type TThostFtdcUOAAutoSendType = ::std::os::raw::c_char;
pub type TThostFtdcQueryDepthType = ::std::os::raw::c_int;
pub type TThostFtdcDataCenterIDType = ::std::os::raw::c_int;
pub type TThostFtdcFlowIDType = ::std::os::raw::c_char;
pub type TThostFtdcCheckLevelType = ::std::os::raw::c_char;
pub type TThostFtdcCheckNoType = ::std::os::raw::c_int;
pub type TThostFtdcCheckStatusType = ::std::os::raw::c_char;
pub type TThostFtdcUsedStatusType = ::std::os::raw::c_char;
pub type TThostFtdcRateTemplateNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcPropertyStringType = [::std::os::raw::c_char; 2049usize];
pub type TThostFtdcBankAcountOriginType = ::std::os::raw::c_char;
pub type TThostFtdcMonthBillTradeSumType = ::std::os::raw::c_char;
pub type TThostFtdcFBTTradeCodeEnumType = ::std::os::raw::c_char;
pub type TThostFtdcRateTemplateIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcRiskRateType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcTimestampType = ::std::os::raw::c_int;
pub type TThostFtdcInvestorIDRuleNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcInvestorIDRuleExprType = [::std::os::raw::c_char; 513usize];
pub type TThostFtdcLastDriftType = ::std::os::raw::c_int;
pub type TThostFtdcLastSuccessType = ::std::os::raw::c_int;
pub type TThostFtdcAuthKeyType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcSerialNumberType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcOTPTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOTPVendorsIDType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcOTPVendorsNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcOTPStatusType = ::std::os::raw::c_char;
pub type TThostFtdcBrokerUserTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFutureTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFundEventTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAccountSourceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCodeSourceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcUserRangeType = ::std::os::raw::c_char;
pub type TThostFtdcTimeSpanType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcImportSequenceIDType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcByGroupType = ::std::os::raw::c_char;
pub type TThostFtdcTradeSumStatModeType = ::std::os::raw::c_char;
pub type TThostFtdcComTypeType = ::std::os::raw::c_int;
pub type TThostFtdcUserProductIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcUserProductNameType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcUserProductMemoType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcCSRCCancelFlagType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcCSRCDateType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcCSRCInvestorNameType = [::std::os::raw::c_char; 201usize];
pub type TThostFtdcCSRCOpenInvestorNameType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcCSRCInvestorIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcCSRCIdentifiedCardNoType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcCSRCClientIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcCSRCBankFlagType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcCSRCBankAccountType = [::std::os::raw::c_char; 23usize];
pub type TThostFtdcCSRCOpenNameType = [::std::os::raw::c_char; 401usize];
pub type TThostFtdcCSRCMemoType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcCSRCTimeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcCSRCTradeIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcCSRCExchangeInstIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcCSRCMortgageNameType = [::std::os::raw::c_char; 7usize];
pub type TThostFtdcCSRCReasonType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcIsSettlementType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcCSRCMoneyType = f64;
pub type TThostFtdcCSRCPriceType = f64;
pub type TThostFtdcCSRCOptionsTypeType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcCSRCStrikePriceType = f64;
pub type TThostFtdcCSRCTargetProductIDType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcCSRCTargetInstrIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcCommModelNameType = [::std::os::raw::c_char; 161usize];
pub type TThostFtdcCommModelMemoType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcExprSetModeType = ::std::os::raw::c_char;
pub type TThostFtdcRateInvestorRangeType = ::std::os::raw::c_char;
pub type TThostFtdcAgentBrokerIDType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcDRIdentityIDType = ::std::os::raw::c_int;
pub type TThostFtdcDRIdentityNameType = [::std::os::raw::c_char; 65usize];
pub type TThostFtdcDBLinkIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcSyncDataStatusType = ::std::os::raw::c_char;
pub type TThostFtdcTradeSourceType = ::std::os::raw::c_char;
pub type TThostFtdcFlexStatModeType = ::std::os::raw::c_char;
pub type TThostFtdcByInvestorRangeType = ::std::os::raw::c_char;
pub type TThostFtdcSRiskRateType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcSequenceNo12Type = ::std::os::raw::c_int;
pub type TThostFtdcPropertyInvestorRangeType = ::std::os::raw::c_char;
pub type TThostFtdcFileStatusType = ::std::os::raw::c_char;
pub type TThostFtdcFileGenStyleType = ::std::os::raw::c_char;
pub type TThostFtdcSysOperModeType = ::std::os::raw::c_char;
pub type TThostFtdcSysOperTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCSRCDataQueyTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFreezeStatusType = ::std::os::raw::c_char;
pub type TThostFtdcStandardStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCSRCFreezeStatusType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcRightParamTypeType = ::std::os::raw::c_char;
pub type TThostFtdcRightTemplateIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcRightTemplateNameType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcDataStatusType = ::std::os::raw::c_char;
pub type TThostFtdcAMLCheckStatusType = ::std::os::raw::c_char;
pub type TThostFtdcAmlDateTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAmlCheckLevelType = ::std::os::raw::c_char;
pub type TThostFtdcAmlCheckFlowType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcDataTypeType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcExportFileTypeType = ::std::os::raw::c_char;
pub type TThostFtdcSettleManagerTypeType = ::std::os::raw::c_char;
pub type TThostFtdcSettleManagerIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcSettleManagerNameType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcSettleManagerLevelType = ::std::os::raw::c_char;
pub type TThostFtdcSettleManagerGroupType = ::std::os::raw::c_char;
pub type TThostFtdcCheckResultMemoType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcFunctionUrlType = [::std::os::raw::c_char; 1025usize];
pub type TThostFtdcAuthInfoType = [::std::os::raw::c_char; 129usize];
pub type TThostFtdcAuthCodeType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcLimitUseTypeType = ::std::os::raw::c_char;
pub type TThostFtdcDataResourceType = ::std::os::raw::c_char;
pub type TThostFtdcMarginTypeType = ::std::os::raw::c_char;
pub type TThostFtdcActiveTypeType = ::std::os::raw::c_char;
pub type TThostFtdcMarginRateTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBackUpStatusType = ::std::os::raw::c_char;
pub type TThostFtdcInitSettlementType = ::std::os::raw::c_char;
pub type TThostFtdcReportStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSaveStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSettArchiveStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCTPTypeType = ::std::os::raw::c_char;
pub type TThostFtdcToolIDType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcToolNameType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcCloseDealTypeType = ::std::os::raw::c_char;
pub type TThostFtdcMortgageFundUseRangeType = ::std::os::raw::c_char;
pub type TThostFtdcCurrencyUnitType = f64;
pub type TThostFtdcExchangeRateType = f64;
pub type TThostFtdcSpecProductTypeType = ::std::os::raw::c_char;
pub type TThostFtdcFundMortgageTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAccountSettlementParamIDType = ::std::os::raw::c_char;
pub type TThostFtdcCurrencyNameType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcCurrencySignType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcFundMortDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcBusinessClassType = ::std::os::raw::c_char;
pub type TThostFtdcSwapSourceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCurrExDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcCurrencySwapStatusType = ::std::os::raw::c_char;
pub type TThostFtdcCurrExchCertNoType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcBatchSerialNoType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcReqFlagType = ::std::os::raw::c_char;
pub type TThostFtdcResFlagType = ::std::os::raw::c_char;
pub type TThostFtdcPageControlType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcRecordCountType = ::std::os::raw::c_int;
pub type TThostFtdcCurrencySwapMemoType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcExStatusType = ::std::os::raw::c_char;
pub type TThostFtdcClientRegionType = ::std::os::raw::c_char;
pub type TThostFtdcWorkPlaceType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcBusinessPeriodType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcWebSiteType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcUOAIdCardTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcClientModeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcInvestorFullNameType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcUOABrokerIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcUOAZipCodeType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcUOAEMailType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcOldCityType = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcCorporateIdentifiedCardNoType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcHasBoardType = ::std::os::raw::c_char;
pub type TThostFtdcStartModeType = ::std::os::raw::c_char;
pub type TThostFtdcTemplateTypeType = ::std::os::raw::c_char;
pub type TThostFtdcLoginModeType = ::std::os::raw::c_char;
pub type TThostFtdcPromptTypeType = ::std::os::raw::c_char;
pub type TThostFtdcLedgerManageIDType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcInvestVarietyType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcBankAccountTypeType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcLedgerManageBankType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcCffexDepartmentNameType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcCffexDepartmentCodeType = [::std::os::raw::c_char; 9usize];
pub type TThostFtdcHasTrusteeType = ::std::os::raw::c_char;
pub type TThostFtdcCSRCMemo1Type = [::std::os::raw::c_char; 41usize];
pub type TThostFtdcAssetmgrCFullNameType = [::std::os::raw::c_char; 101usize];
pub type TThostFtdcAssetmgrApprovalNOType = [::std::os::raw::c_char; 51usize];
pub type TThostFtdcAssetmgrMgrNameType = [::std::os::raw::c_char; 401usize];
pub type TThostFtdcAmTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCSRCAmTypeType = [::std::os::raw::c_char; 5usize];
pub type TThostFtdcCSRCFundIOTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCusAccountTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCSRCNationalType = [::std::os::raw::c_char; 4usize];
pub type TThostFtdcCSRCSecAgentIDType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcLanguageTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAmAccountType = [::std::os::raw::c_char; 23usize];
pub type TThostFtdcAssetmgrClientTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAssetmgrTypeType = ::std::os::raw::c_char;
pub type TThostFtdcUOMType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcSHFEInstLifePhaseType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcSHFEProductClassType = [::std::os::raw::c_char; 11usize];
pub type TThostFtdcPriceDecimalType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcInTheMoneyFlagType = [::std::os::raw::c_char; 2usize];
pub type TThostFtdcCheckInstrTypeType = ::std::os::raw::c_char;
pub type TThostFtdcDeliveryTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBigMoneyType = f64;
pub type TThostFtdcMaxMarginSideAlgorithmType = ::std::os::raw::c_char;
pub type TThostFtdcDAClientTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCombinInstrIDType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcCombinSettlePriceType = [::std::os::raw::c_char; 61usize];
pub type TThostFtdcDCEPriorityType = ::std::os::raw::c_int;
pub type TThostFtdcTradeGroupIDType = ::std::os::raw::c_int;
pub type TThostFtdcIsCheckPrepaType = ::std::os::raw::c_int;
pub type TThostFtdcUOAAssetmgrTypeType = ::std::os::raw::c_char;
pub type TThostFtdcDirectionEnType = ::std::os::raw::c_char;
pub type TThostFtdcOffsetFlagEnType = ::std::os::raw::c_char;
pub type TThostFtdcHedgeFlagEnType = ::std::os::raw::c_char;
pub type TThostFtdcFundIOTypeEnType = ::std::os::raw::c_char;
pub type TThostFtdcFundTypeEnType = ::std::os::raw::c_char;
pub type TThostFtdcFundDirectionEnType = ::std::os::raw::c_char;
pub type TThostFtdcFundMortDirectionEnType = ::std::os::raw::c_char;
pub type TThostFtdcSwapBusinessTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcOptionsTypeType = ::std::os::raw::c_char;
pub type TThostFtdcStrikeModeType = ::std::os::raw::c_char;
pub type TThostFtdcStrikeTypeType = ::std::os::raw::c_char;
pub type TThostFtdcApplyTypeType = ::std::os::raw::c_char;
pub type TThostFtdcGiveUpDataSourceType = ::std::os::raw::c_char;
pub type TThostFtdcExecOrderSysIDType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcExecResultType = ::std::os::raw::c_char;
pub type TThostFtdcStrikeSequenceType = ::std::os::raw::c_int;
pub type TThostFtdcStrikeTimeType = [::std::os::raw::c_char; 13usize];
pub type TThostFtdcCombinationTypeType = ::std::os::raw::c_char;
pub type TThostFtdcDceCombinationTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOptionRoyaltyPriceTypeType = ::std::os::raw::c_char;
pub type TThostFtdcBalanceAlgorithmType = ::std::os::raw::c_char;
pub type TThostFtdcActionTypeType = ::std::os::raw::c_char;
pub type TThostFtdcForQuoteStatusType = ::std::os::raw::c_char;
pub type TThostFtdcValueMethodType = ::std::os::raw::c_char;
pub type TThostFtdcExecOrderPositionFlagType = ::std::os::raw::c_char;
pub type TThostFtdcExecOrderCloseFlagType = ::std::os::raw::c_char;
pub type TThostFtdcProductTypeType = ::std::os::raw::c_char;
pub type TThostFtdcCZCEUploadFileNameType = ::std::os::raw::c_char;
pub type TThostFtdcDCEUploadFileNameType = ::std::os::raw::c_char;
pub type TThostFtdcSHFEUploadFileNameType = ::std::os::raw::c_char;
pub type TThostFtdcCFFEXUploadFileNameType = ::std::os::raw::c_char;
pub type TThostFtdcCombDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcStrikeOffsetTypeType = ::std::os::raw::c_char;
pub type TThostFtdcReserveOpenAccStasType = ::std::os::raw::c_char;
pub type TThostFtdcLoginRemarkType = [::std::os::raw::c_char; 36usize];
pub type TThostFtdcInvestUnitIDType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcBulletinIDType = ::std::os::raw::c_int;
pub type TThostFtdcNewsTypeType = [::std::os::raw::c_char; 3usize];
pub type TThostFtdcNewsUrgencyType = ::std::os::raw::c_char;
pub type TThostFtdcAbstractType = [::std::os::raw::c_char; 81usize];
pub type TThostFtdcComeFromType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcURLLinkType = [::std::os::raw::c_char; 201usize];
pub type TThostFtdcLongIndividualNameType = [::std::os::raw::c_char; 161usize];
pub type TThostFtdcLongFBEBankAccountNameType = [::std::os::raw::c_char; 161usize];
pub type TThostFtdcDateTimeType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcWeakPasswordSourceType = ::std::os::raw::c_char;
pub type TThostFtdcRandomStringType = [::std::os::raw::c_char; 17usize];
pub type TThostFtdcOptSelfCloseFlagType = ::std::os::raw::c_char;
pub type TThostFtdcBizTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAppTypeType = ::std::os::raw::c_char;
pub type TThostFtdcAppIDType = [::std::os::raw::c_char; 33usize];
pub type TThostFtdcSystemInfoLenType = ::std::os::raw::c_int;
pub type TThostFtdcAdditionalInfoLenType = ::std::os::raw::c_int;
pub type TThostFtdcClientSystemInfoType = [::std::os::raw::c_char; 273usize];
pub type TThostFtdcAdditionalInfoType = [::std::os::raw::c_char; 261usize];
pub type TThostFtdcBase64ClientSystemInfoType = [::std::os::raw::c_char; 365usize];
pub type TThostFtdcBase64AdditionalInfoType = [::std::os::raw::c_char; 349usize];
pub type TThostFtdcCurrentAuthMethodType = ::std::os::raw::c_int;
pub type TThostFtdcCaptchaInfoLenType = ::std::os::raw::c_int;
pub type TThostFtdcCaptchaInfoType = [::std::os::raw::c_char; 2561usize];
pub type TThostFtdcUserTextSeqType = ::std::os::raw::c_int;
pub type TThostFtdcHandshakeDataType = [::std::os::raw::c_char; 301usize];
pub type TThostFtdcHandshakeDataLenType = ::std::os::raw::c_int;
pub type TThostFtdcCryptoKeyVersionType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcRsaKeyVersionType = ::std::os::raw::c_int;
pub type TThostFtdcSoftwareProviderIDType = [::std::os::raw::c_char; 22usize];
pub type TThostFtdcCollectTimeType = [::std::os::raw::c_char; 21usize];
pub type TThostFtdcQueryFreqType = ::std::os::raw::c_int;
pub type TThostFtdcResponseValueType = ::std::os::raw::c_char;
pub type TThostFtdcOTCTradeTypeType = ::std::os::raw::c_char;
pub type TThostFtdcMatchTypeType = ::std::os::raw::c_char;
pub type TThostFtdcOTCTraderIDType = [::std::os::raw::c_char; 31usize];
pub type TThostFtdcRiskValueType = f64;
pub type TThostFtdcIDBNameType = [::std::os::raw::c_char; 100usize];
pub type TThostFtdcDiscountRatioType = f64;
pub type TThostFtdcAuthTypeType = ::std::os::raw::c_char;
pub type TThostFtdcClassTypeType = ::std::os::raw::c_char;
pub type TThostFtdcTradingTypeType = ::std::os::raw::c_char;
pub type TThostFtdcProductStatusType = ::std::os::raw::c_char;
pub type TThostFtdcSyncDeltaStatusType = ::std::os::raw::c_char;
pub type TThostFtdcActionDirectionType = ::std::os::raw::c_char;
pub type TThostFtdcOrderCancelAlgType = ::std::os::raw::c_char;
pub type TThostFtdcSyncDescriptionType = [::std::os::raw::c_char; 257usize];
pub type TThostFtdcCommonIntType = ::std::os::raw::c_int;
pub type TThostFtdcSysVersionType = [::std::os::raw::c_char; 41usize];
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDisseminationField {
    pub SequenceSeries: TThostFtdcSequenceSeriesType,
    pub SequenceNo: TThostFtdcSequenceNoType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserLoginField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub OneTimePassword: TThostFtdcPasswordType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcReqUserLoginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspUserLoginField {
    pub TradingDay: TThostFtdcDateType,
    pub LoginTime: TThostFtdcTimeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub SystemName: TThostFtdcSystemNameType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub MaxOrderRef: TThostFtdcOrderRefType,
    pub SHFETime: TThostFtdcTimeType,
    pub DCETime: TThostFtdcTimeType,
    pub CZCETime: TThostFtdcTimeType,
    pub FFEXTime: TThostFtdcTimeType,
    pub INETime: TThostFtdcTimeType,
    pub SysVersion: TThostFtdcSysVersionType,
}
impl Default for CThostFtdcRspUserLoginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserLogoutField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcForceUserLogoutField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqAuthenticateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub AuthCode: TThostFtdcAuthCodeType,
    pub AppID: TThostFtdcAppIDType,
}
impl Default for CThostFtdcReqAuthenticateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspAuthenticateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub AppID: TThostFtdcAppIDType,
    pub AppType: TThostFtdcAppTypeType,
}
impl Default for CThostFtdcRspAuthenticateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAuthenticationInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub AuthInfo: TThostFtdcAuthInfoType,
    pub IsResult: TThostFtdcBoolType,
    pub AppID: TThostFtdcAppIDType,
    pub AppType: TThostFtdcAppTypeType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcAuthenticationInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspUserLogin2Field {
    pub TradingDay: TThostFtdcDateType,
    pub LoginTime: TThostFtdcTimeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub SystemName: TThostFtdcSystemNameType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub MaxOrderRef: TThostFtdcOrderRefType,
    pub SHFETime: TThostFtdcTimeType,
    pub DCETime: TThostFtdcTimeType,
    pub CZCETime: TThostFtdcTimeType,
    pub FFEXTime: TThostFtdcTimeType,
    pub INETime: TThostFtdcTimeType,
    pub RandomString: TThostFtdcRandomStringType,
}
impl Default for CThostFtdcRspUserLogin2Field {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferHeaderField {
    pub Version: TThostFtdcVersionType,
    pub TradeCode: TThostFtdcTradeCodeType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub TradeSerial: TThostFtdcTradeSerialType,
    pub FutureID: TThostFtdcFutureIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
    pub OperNo: TThostFtdcOperNoType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub RecordNum: TThostFtdcRecordNumType,
    pub SessionID: TThostFtdcSessionIDType,
    pub RequestID: TThostFtdcRequestIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferBankToFutureReqField {
    pub FutureAccount: TThostFtdcAccountIDType,
    pub FuturePwdFlag: TThostFtdcFuturePwdFlagType,
    pub FutureAccPwd: TThostFtdcFutureAccPwdType,
    pub TradeAmt: TThostFtdcMoneyType,
    pub CustFee: TThostFtdcMoneyType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferBankToFutureRspField {
    pub RetCode: TThostFtdcRetCodeType,
    pub RetInfo: TThostFtdcRetInfoType,
    pub FutureAccount: TThostFtdcAccountIDType,
    pub TradeAmt: TThostFtdcMoneyType,
    pub CustFee: TThostFtdcMoneyType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
impl Default for CThostFtdcTransferBankToFutureRspField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferFutureToBankReqField {
    pub FutureAccount: TThostFtdcAccountIDType,
    pub FuturePwdFlag: TThostFtdcFuturePwdFlagType,
    pub FutureAccPwd: TThostFtdcFutureAccPwdType,
    pub TradeAmt: TThostFtdcMoneyType,
    pub CustFee: TThostFtdcMoneyType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferFutureToBankRspField {
    pub RetCode: TThostFtdcRetCodeType,
    pub RetInfo: TThostFtdcRetInfoType,
    pub FutureAccount: TThostFtdcAccountIDType,
    pub TradeAmt: TThostFtdcMoneyType,
    pub CustFee: TThostFtdcMoneyType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
impl Default for CThostFtdcTransferFutureToBankRspField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferQryBankReqField {
    pub FutureAccount: TThostFtdcAccountIDType,
    pub FuturePwdFlag: TThostFtdcFuturePwdFlagType,
    pub FutureAccPwd: TThostFtdcFutureAccPwdType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferQryBankRspField {
    pub RetCode: TThostFtdcRetCodeType,
    pub RetInfo: TThostFtdcRetInfoType,
    pub FutureAccount: TThostFtdcAccountIDType,
    pub TradeAmt: TThostFtdcMoneyType,
    pub UseAmt: TThostFtdcMoneyType,
    pub FetchAmt: TThostFtdcMoneyType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
}
impl Default for CThostFtdcTransferQryBankRspField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferQryDetailReqField {
    pub FutureAccount: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferQryDetailRspField {
    pub TradeDate: TThostFtdcDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub TradeCode: TThostFtdcTradeCodeType,
    pub FutureSerial: TThostFtdcTradeSerialNoType,
    pub FutureID: TThostFtdcFutureIDType,
    pub FutureAccount: TThostFtdcFutureAccountType,
    pub BankSerial: TThostFtdcTradeSerialNoType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub CertCode: TThostFtdcCertCodeType,
    pub CurrencyCode: TThostFtdcCurrencyCodeType,
    pub TxAmount: TThostFtdcMoneyType,
    pub Flag: TThostFtdcTransferValidFlagType,
}
impl Default for CThostFtdcTransferQryDetailRspField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspInfoField {
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcRspInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExchangeName: TThostFtdcExchangeNameType,
    pub ExchangeProperty: TThostFtdcExchangePropertyType,
}
impl Default for CThostFtdcExchangeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcProductField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ProductName: TThostFtdcProductNameType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductClass: TThostFtdcProductClassType,
    pub VolumeMultiple: TThostFtdcVolumeMultipleType,
    pub PriceTick: TThostFtdcPriceType,
    pub MaxMarketOrderVolume: TThostFtdcVolumeType,
    pub MinMarketOrderVolume: TThostFtdcVolumeType,
    pub MaxLimitOrderVolume: TThostFtdcVolumeType,
    pub MinLimitOrderVolume: TThostFtdcVolumeType,
    pub PositionType: TThostFtdcPositionTypeType,
    pub PositionDateType: TThostFtdcPositionDateTypeType,
    pub CloseDealType: TThostFtdcCloseDealTypeType,
    pub TradeCurrencyID: TThostFtdcCurrencyIDType,
    pub MortgageFundUseRange: TThostFtdcMortgageFundUseRangeType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub UnderlyingMultiple: TThostFtdcUnderlyingMultipleType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub ExchangeProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcProductField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentName: TThostFtdcInstrumentNameType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub reserve3: TThostFtdcOldInstrumentIDType,
    pub ProductClass: TThostFtdcProductClassType,
    pub DeliveryYear: TThostFtdcYearType,
    pub DeliveryMonth: TThostFtdcMonthType,
    pub MaxMarketOrderVolume: TThostFtdcVolumeType,
    pub MinMarketOrderVolume: TThostFtdcVolumeType,
    pub MaxLimitOrderVolume: TThostFtdcVolumeType,
    pub MinLimitOrderVolume: TThostFtdcVolumeType,
    pub VolumeMultiple: TThostFtdcVolumeMultipleType,
    pub PriceTick: TThostFtdcPriceType,
    pub CreateDate: TThostFtdcDateType,
    pub OpenDate: TThostFtdcDateType,
    pub ExpireDate: TThostFtdcDateType,
    pub StartDelivDate: TThostFtdcDateType,
    pub EndDelivDate: TThostFtdcDateType,
    pub InstLifePhase: TThostFtdcInstLifePhaseType,
    pub IsTrading: TThostFtdcBoolType,
    pub PositionType: TThostFtdcPositionTypeType,
    pub PositionDateType: TThostFtdcPositionDateTypeType,
    pub LongMarginRatio: TThostFtdcRatioType,
    pub ShortMarginRatio: TThostFtdcRatioType,
    pub MaxMarginSideAlgorithm: TThostFtdcMaxMarginSideAlgorithmType,
    pub reserve4: TThostFtdcOldInstrumentIDType,
    pub StrikePrice: TThostFtdcPriceType,
    pub OptionsType: TThostFtdcOptionsTypeType,
    pub UnderlyingMultiple: TThostFtdcUnderlyingMultipleType,
    pub CombinationType: TThostFtdcCombinationTypeType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub UnderlyingInstrID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerAbbr: TThostFtdcBrokerAbbrType,
    pub BrokerName: TThostFtdcBrokerNameType,
    pub IsActive: TThostFtdcBoolType,
}
impl Default for CThostFtdcBrokerField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTraderField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallCount: TThostFtdcInstallCountType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub OrderCancelAlg: TThostFtdcOrderCancelAlgType,
}
impl Default for CThostFtdcTraderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorField {
    pub InvestorID: TThostFtdcInvestorIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorGroupID: TThostFtdcInvestorIDType,
    pub InvestorName: TThostFtdcPartyNameType,
    pub IdentifiedCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub IsActive: TThostFtdcBoolType,
    pub Telephone: TThostFtdcTelephoneType,
    pub Address: TThostFtdcAddressType,
    pub OpenDate: TThostFtdcDateType,
    pub Mobile: TThostFtdcMobileType,
    pub CommModelID: TThostFtdcInvestorIDType,
    pub MarginModelID: TThostFtdcInvestorIDType,
}
impl Default for CThostFtdcInvestorField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingCodeField {
    pub InvestorID: TThostFtdcInvestorIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub IsActive: TThostFtdcBoolType,
    pub ClientIDType: TThostFtdcClientIDTypeType,
    pub BranchID: TThostFtdcBranchIDType,
    pub BizType: TThostFtdcBizTypeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcPartBrokerField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub IsActive: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSuperUserField {
    pub UserID: TThostFtdcUserIDType,
    pub UserName: TThostFtdcUserNameType,
    pub Password: TThostFtdcPasswordType,
    pub IsActive: TThostFtdcBoolType,
}
impl Default for CThostFtdcSuperUserField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSuperUserFunctionField {
    pub UserID: TThostFtdcUserIDType,
    pub FunctionCode: TThostFtdcFunctionCodeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorGroupField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorGroupID: TThostFtdcInvestorIDType,
    pub InvestorGroupName: TThostFtdcInvestorGroupNameType,
}
impl Default for CThostFtdcInvestorGroupField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingAccountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub PreMortgage: TThostFtdcMoneyType,
    pub PreCredit: TThostFtdcMoneyType,
    pub PreDeposit: TThostFtdcMoneyType,
    pub PreBalance: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub InterestBase: TThostFtdcMoneyType,
    pub Interest: TThostFtdcMoneyType,
    pub Deposit: TThostFtdcMoneyType,
    pub Withdraw: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CurrMargin: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub Balance: TThostFtdcMoneyType,
    pub Available: TThostFtdcMoneyType,
    pub WithdrawQuota: TThostFtdcMoneyType,
    pub Reserve: TThostFtdcMoneyType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub Credit: TThostFtdcMoneyType,
    pub Mortgage: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub DeliveryMargin: TThostFtdcMoneyType,
    pub ExchangeDeliveryMargin: TThostFtdcMoneyType,
    pub ReserveBalance: TThostFtdcMoneyType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub PreFundMortgageIn: TThostFtdcMoneyType,
    pub PreFundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageIn: TThostFtdcMoneyType,
    pub FundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageAvailable: TThostFtdcMoneyType,
    pub MortgageableFund: TThostFtdcMoneyType,
    pub SpecProductMargin: TThostFtdcMoneyType,
    pub SpecProductFrozenMargin: TThostFtdcMoneyType,
    pub SpecProductCommission: TThostFtdcMoneyType,
    pub SpecProductFrozenCommission: TThostFtdcMoneyType,
    pub SpecProductPositionProfit: TThostFtdcMoneyType,
    pub SpecProductCloseProfit: TThostFtdcMoneyType,
    pub SpecProductPositionProfitByAlg: TThostFtdcMoneyType,
    pub SpecProductExchangeMargin: TThostFtdcMoneyType,
    pub BizType: TThostFtdcBizTypeType,
    pub FrozenSwap: TThostFtdcMoneyType,
    pub RemainSwap: TThostFtdcMoneyType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorPositionField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub PositionDate: TThostFtdcPositionDateType,
    pub YdPosition: TThostFtdcVolumeType,
    pub Position: TThostFtdcVolumeType,
    pub LongFrozen: TThostFtdcVolumeType,
    pub ShortFrozen: TThostFtdcVolumeType,
    pub LongFrozenAmount: TThostFtdcMoneyType,
    pub ShortFrozenAmount: TThostFtdcMoneyType,
    pub OpenVolume: TThostFtdcVolumeType,
    pub CloseVolume: TThostFtdcVolumeType,
    pub OpenAmount: TThostFtdcMoneyType,
    pub CloseAmount: TThostFtdcMoneyType,
    pub PositionCost: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub UseMargin: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OpenCost: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub CombPosition: TThostFtdcVolumeType,
    pub CombLongFrozen: TThostFtdcVolumeType,
    pub CombShortFrozen: TThostFtdcVolumeType,
    pub CloseProfitByDate: TThostFtdcMoneyType,
    pub CloseProfitByTrade: TThostFtdcMoneyType,
    pub TodayPosition: TThostFtdcVolumeType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub StrikeFrozen: TThostFtdcVolumeType,
    pub StrikeFrozenAmount: TThostFtdcMoneyType,
    pub AbandonFrozen: TThostFtdcVolumeType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub YdStrikeFrozen: TThostFtdcVolumeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub PositionCostOffset: TThostFtdcMoneyType,
    pub TasPosition: TThostFtdcVolumeType,
    pub TasPositionCost: TThostFtdcMoneyType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInvestorPositionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentMarginRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentCommissionRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub BizType: TThostFtdcBizTypeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentCommissionRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDepthMarketDataField {
    pub TradingDay: TThostFtdcDateType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub LastPrice: TThostFtdcPriceType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub PreClosePrice: TThostFtdcPriceType,
    pub PreOpenInterest: TThostFtdcLargeVolumeType,
    pub OpenPrice: TThostFtdcPriceType,
    pub HighestPrice: TThostFtdcPriceType,
    pub LowestPrice: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub Turnover: TThostFtdcMoneyType,
    pub OpenInterest: TThostFtdcLargeVolumeType,
    pub ClosePrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub UpperLimitPrice: TThostFtdcPriceType,
    pub LowerLimitPrice: TThostFtdcPriceType,
    pub PreDelta: TThostFtdcRatioType,
    pub CurrDelta: TThostFtdcRatioType,
    pub UpdateTime: TThostFtdcTimeType,
    pub UpdateMillisec: TThostFtdcMillisecType,
    pub BidPrice1: TThostFtdcPriceType,
    pub BidVolume1: TThostFtdcVolumeType,
    pub AskPrice1: TThostFtdcPriceType,
    pub AskVolume1: TThostFtdcVolumeType,
    pub BidPrice2: TThostFtdcPriceType,
    pub BidVolume2: TThostFtdcVolumeType,
    pub AskPrice2: TThostFtdcPriceType,
    pub AskVolume2: TThostFtdcVolumeType,
    pub BidPrice3: TThostFtdcPriceType,
    pub BidVolume3: TThostFtdcVolumeType,
    pub AskPrice3: TThostFtdcPriceType,
    pub AskVolume3: TThostFtdcVolumeType,
    pub BidPrice4: TThostFtdcPriceType,
    pub BidVolume4: TThostFtdcVolumeType,
    pub AskPrice4: TThostFtdcPriceType,
    pub AskVolume4: TThostFtdcVolumeType,
    pub BidPrice5: TThostFtdcPriceType,
    pub BidVolume5: TThostFtdcVolumeType,
    pub AskPrice5: TThostFtdcPriceType,
    pub AskVolume5: TThostFtdcVolumeType,
    pub AveragePrice: TThostFtdcPriceType,
    pub ActionDay: TThostFtdcDateType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub BandingUpperPrice: TThostFtdcPriceType,
    pub BandingLowerPrice: TThostFtdcPriceType,
}
impl Default for CThostFtdcDepthMarketDataField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentTradingRightField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub TradingRight: TThostFtdcTradingRightType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentTradingRightField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserName: TThostFtdcUserNameType,
    pub UserType: TThostFtdcUserTypeType,
    pub IsActive: TThostFtdcBoolType,
    pub IsUsingOTP: TThostFtdcBoolType,
    pub IsAuthForce: TThostFtdcBoolType,
}
impl Default for CThostFtdcBrokerUserField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserPasswordField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub LastUpdateTime: TThostFtdcDateTimeType,
    pub LastLoginTime: TThostFtdcDateTimeType,
    pub ExpireDate: TThostFtdcDateType,
    pub WeakExpireDate: TThostFtdcDateType,
}
impl Default for CThostFtdcBrokerUserPasswordField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserFunctionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub BrokerFunctionCode: TThostFtdcBrokerFunctionCodeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTraderOfferField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub TraderConnectStatus: TThostFtdcTraderConnectStatusType,
    pub ConnectRequestDate: TThostFtdcDateType,
    pub ConnectRequestTime: TThostFtdcTimeType,
    pub LastReportDate: TThostFtdcDateType,
    pub LastReportTime: TThostFtdcTimeType,
    pub ConnectDate: TThostFtdcDateType,
    pub ConnectTime: TThostFtdcTimeType,
    pub StartDate: TThostFtdcDateType,
    pub StartTime: TThostFtdcTimeType,
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub MaxTradeID: TThostFtdcTradeIDType,
    pub MaxOrderMessageReference: TThostFtdcReturnCodeType,
    pub OrderCancelAlg: TThostFtdcOrderCancelAlgType,
}
impl Default for CThostFtdcTraderOfferField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSettlementInfoField {
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub Content: TThostFtdcContentType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcSettlementInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentMarginRateAdjustField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentMarginRateAdjustField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeMarginRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcExchangeMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeMarginRateAdjustField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ExchLongMarginRatioByMoney: TThostFtdcRatioType,
    pub ExchLongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ExchShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ExchShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub NoLongMarginRatioByMoney: TThostFtdcRatioType,
    pub NoLongMarginRatioByVolume: TThostFtdcMoneyType,
    pub NoShortMarginRatioByMoney: TThostFtdcRatioType,
    pub NoShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcExchangeMarginRateAdjustField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub FromCurrencyID: TThostFtdcCurrencyIDType,
    pub FromCurrencyUnit: TThostFtdcCurrencyUnitType,
    pub ToCurrencyID: TThostFtdcCurrencyIDType,
    pub ExchangeRate: TThostFtdcExchangeRateType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSettlementRefField {
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCurrentTimeField {
    pub CurrDate: TThostFtdcDateType,
    pub CurrTime: TThostFtdcTimeType,
    pub CurrMillisec: TThostFtdcMillisecType,
    pub ActionDay: TThostFtdcDateType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCommPhaseField {
    pub TradingDay: TThostFtdcDateType,
    pub CommPhaseNo: TThostFtdcCommPhaseNoType,
    pub SystemID: TThostFtdcSystemIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLoginInfoField {
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub LoginDate: TThostFtdcDateType,
    pub LoginTime: TThostFtdcTimeType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub SystemName: TThostFtdcSystemNameType,
    pub PasswordDeprecated: TThostFtdcPasswordType,
    pub MaxOrderRef: TThostFtdcOrderRefType,
    pub SHFETime: TThostFtdcTimeType,
    pub DCETime: TThostFtdcTimeType,
    pub CZCETime: TThostFtdcTimeType,
    pub FFEXTime: TThostFtdcTimeType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub OneTimePassword: TThostFtdcPasswordType,
    pub INETime: TThostFtdcTimeType,
    pub IsQryControl: TThostFtdcBoolType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub Password: TThostFtdcPasswordType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcLoginInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLogoutAllField {
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub SystemName: TThostFtdcSystemNameType,
}
impl Default for CThostFtdcLogoutAllField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcFrontStatusField {
    pub FrontID: TThostFtdcFrontIDType,
    pub LastReportDate: TThostFtdcDateType,
    pub LastReportTime: TThostFtdcTimeType,
    pub IsActive: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserPasswordUpdateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub OldPassword: TThostFtdcPasswordType,
    pub NewPassword: TThostFtdcPasswordType,
}
impl Default for CThostFtdcUserPasswordUpdateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub UserForceClose: TThostFtdcBoolType,
    pub IsSwapOrder: TThostFtdcBoolType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub OrderSource: TThostFtdcOrderSourceType,
    pub OrderStatus: TThostFtdcOrderStatusType,
    pub OrderType: TThostFtdcOrderTypeType,
    pub VolumeTraded: TThostFtdcVolumeType,
    pub VolumeTotal: TThostFtdcVolumeType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub ActiveTime: TThostFtdcTimeType,
    pub SuspendTime: TThostFtdcTimeType,
    pub UpdateTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ActiveTraderID: TThostFtdcTraderIDType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub UserForceClose: TThostFtdcBoolType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerOrderSeq: TThostFtdcSequenceNoType,
    pub RelativeOrderSysID: TThostFtdcOrderSysIDType,
    pub ZCETotalTradedVolume: TThostFtdcVolumeType,
    pub IsSwapOrder: TThostFtdcBoolType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOrderField {
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub OrderSource: TThostFtdcOrderSourceType,
    pub OrderStatus: TThostFtdcOrderStatusType,
    pub OrderType: TThostFtdcOrderTypeType,
    pub VolumeTraded: TThostFtdcVolumeType,
    pub VolumeTotal: TThostFtdcVolumeType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub ActiveTime: TThostFtdcTimeType,
    pub SuspendTime: TThostFtdcTimeType,
    pub UpdateTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ActiveTraderID: TThostFtdcTraderIDType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOrderInsertErrorField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcExchangeOrderInsertErrorField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeChange: TThostFtdcVolumeType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeChange: TThostFtdcVolumeType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOrderActionField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeChange: TThostFtdcVolumeType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOrderActionErrorField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcExchangeOrderActionErrorField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeTradeField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TradeID: TThostFtdcTradeIDType,
    pub Direction: TThostFtdcDirectionType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub TradingRole: TThostFtdcTradingRoleType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Price: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub TradeDate: TThostFtdcDateType,
    pub TradeTime: TThostFtdcTimeType,
    pub TradeType: TThostFtdcTradeTypeType,
    pub PriceSource: TThostFtdcPriceSourceType,
    pub TraderID: TThostFtdcTraderIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub TradeSource: TThostFtdcTradeSourceType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcExchangeTradeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TradeID: TThostFtdcTradeIDType,
    pub Direction: TThostFtdcDirectionType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub TradingRole: TThostFtdcTradingRoleType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Price: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub TradeDate: TThostFtdcDateType,
    pub TradeTime: TThostFtdcTimeType,
    pub TradeType: TThostFtdcTradeTypeType,
    pub PriceSource: TThostFtdcPriceSourceType,
    pub TraderID: TThostFtdcTraderIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub BrokerOrderSeq: TThostFtdcSequenceNoType,
    pub TradeSource: TThostFtdcTradeSourceType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcTradeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserSessionField {
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub LoginDate: TThostFtdcDateType,
    pub LoginTime: TThostFtdcTimeType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcUserSessionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMaxOrderVolumeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub MaxVolume: TThostFtdcVolumeType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryMaxOrderVolumeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSettlementInfoConfirmField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ConfirmDate: TThostFtdcDateType,
    pub ConfirmTime: TThostFtdcTimeType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDepositField {
    pub DepositSeqNo: TThostFtdcDepositSeqNoType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Deposit: TThostFtdcMoneyType,
    pub IsForce: TThostFtdcBoolType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub IsFromSopt: TThostFtdcBoolType,
    pub TradingPassword: TThostFtdcPasswordType,
}
impl Default for CThostFtdcSyncDepositField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncFundMortgageField {
    pub MortgageSeqNo: TThostFtdcDepositSeqNoType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub FromCurrencyID: TThostFtdcCurrencyIDType,
    pub MortgageAmount: TThostFtdcMoneyType,
    pub ToCurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerSyncField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInvestorField {
    pub InvestorID: TThostFtdcInvestorIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorGroupID: TThostFtdcInvestorIDType,
    pub InvestorName: TThostFtdcPartyNameType,
    pub IdentifiedCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub IsActive: TThostFtdcBoolType,
    pub Telephone: TThostFtdcTelephoneType,
    pub Address: TThostFtdcAddressType,
    pub OpenDate: TThostFtdcDateType,
    pub Mobile: TThostFtdcMobileType,
    pub CommModelID: TThostFtdcInvestorIDType,
    pub MarginModelID: TThostFtdcInvestorIDType,
}
impl Default for CThostFtdcSyncingInvestorField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingTradingCodeField {
    pub InvestorID: TThostFtdcInvestorIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub IsActive: TThostFtdcBoolType,
    pub ClientIDType: TThostFtdcClientIDTypeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInvestorGroupField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorGroupID: TThostFtdcInvestorIDType,
    pub InvestorGroupName: TThostFtdcInvestorGroupNameType,
}
impl Default for CThostFtdcSyncingInvestorGroupField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingTradingAccountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub PreMortgage: TThostFtdcMoneyType,
    pub PreCredit: TThostFtdcMoneyType,
    pub PreDeposit: TThostFtdcMoneyType,
    pub PreBalance: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub InterestBase: TThostFtdcMoneyType,
    pub Interest: TThostFtdcMoneyType,
    pub Deposit: TThostFtdcMoneyType,
    pub Withdraw: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CurrMargin: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub Balance: TThostFtdcMoneyType,
    pub Available: TThostFtdcMoneyType,
    pub WithdrawQuota: TThostFtdcMoneyType,
    pub Reserve: TThostFtdcMoneyType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub Credit: TThostFtdcMoneyType,
    pub Mortgage: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub DeliveryMargin: TThostFtdcMoneyType,
    pub ExchangeDeliveryMargin: TThostFtdcMoneyType,
    pub ReserveBalance: TThostFtdcMoneyType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub PreFundMortgageIn: TThostFtdcMoneyType,
    pub PreFundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageIn: TThostFtdcMoneyType,
    pub FundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageAvailable: TThostFtdcMoneyType,
    pub MortgageableFund: TThostFtdcMoneyType,
    pub SpecProductMargin: TThostFtdcMoneyType,
    pub SpecProductFrozenMargin: TThostFtdcMoneyType,
    pub SpecProductCommission: TThostFtdcMoneyType,
    pub SpecProductFrozenCommission: TThostFtdcMoneyType,
    pub SpecProductPositionProfit: TThostFtdcMoneyType,
    pub SpecProductCloseProfit: TThostFtdcMoneyType,
    pub SpecProductPositionProfitByAlg: TThostFtdcMoneyType,
    pub SpecProductExchangeMargin: TThostFtdcMoneyType,
    pub FrozenSwap: TThostFtdcMoneyType,
    pub RemainSwap: TThostFtdcMoneyType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInvestorPositionField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub PositionDate: TThostFtdcPositionDateType,
    pub YdPosition: TThostFtdcVolumeType,
    pub Position: TThostFtdcVolumeType,
    pub LongFrozen: TThostFtdcVolumeType,
    pub ShortFrozen: TThostFtdcVolumeType,
    pub LongFrozenAmount: TThostFtdcMoneyType,
    pub ShortFrozenAmount: TThostFtdcMoneyType,
    pub OpenVolume: TThostFtdcVolumeType,
    pub CloseVolume: TThostFtdcVolumeType,
    pub OpenAmount: TThostFtdcMoneyType,
    pub CloseAmount: TThostFtdcMoneyType,
    pub PositionCost: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub UseMargin: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OpenCost: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub CombPosition: TThostFtdcVolumeType,
    pub CombLongFrozen: TThostFtdcVolumeType,
    pub CombShortFrozen: TThostFtdcVolumeType,
    pub CloseProfitByDate: TThostFtdcMoneyType,
    pub CloseProfitByTrade: TThostFtdcMoneyType,
    pub TodayPosition: TThostFtdcVolumeType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub StrikeFrozen: TThostFtdcVolumeType,
    pub StrikeFrozenAmount: TThostFtdcMoneyType,
    pub AbandonFrozen: TThostFtdcVolumeType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub YdStrikeFrozen: TThostFtdcVolumeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub PositionCostOffset: TThostFtdcMoneyType,
    pub TasPosition: TThostFtdcVolumeType,
    pub TasPositionCost: TThostFtdcMoneyType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcSyncingInvestorPositionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInstrumentMarginRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcSyncingInstrumentMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInstrumentCommissionRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcSyncingInstrumentCommissionRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncingInstrumentTradingRightField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub TradingRight: TThostFtdcTradingRightType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcSyncingInstrumentTradingRightField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTradeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TradeID: TThostFtdcTradeIDType,
    pub TradeTimeStart: TThostFtdcTimeType,
    pub TradeTimeEnd: TThostFtdcTimeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryTradeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorPositionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInvestorPositionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTradingAccountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub BizType: TThostFtdcBizTypeType,
    pub AccountID: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTradingCodeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub ClientIDType: TThostFtdcClientIDTypeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorGroupField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentMarginRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInstrumentMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentCommissionRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInstrumentCommissionRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentTradingRightField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInstrumentTradingRightField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTraderField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySuperUserFunctionField {
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryUserSessionField {
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryPartBrokerField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryFrontStatusField {
    pub FrontID: TThostFtdcFrontIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeOrderField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryExchangeOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeOrderActionField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySuperUserField {
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeField {
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryProductField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ProductClass: TThostFtdcProductClassType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryProductField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub reserve3: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryDepthMarketDataField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryDepthMarketDataField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerUserField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerUserFunctionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTraderOfferField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySyncDepositField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub DepositSeqNo: TThostFtdcDepositSeqNoType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySettlementInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub TradingDay: TThostFtdcDateType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeMarginRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryExchangeMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeMarginRateAdjustField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryExchangeMarginRateAdjustField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub FromCurrencyID: TThostFtdcCurrencyIDType,
    pub ToCurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySyncFundMortgageField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub MortgageSeqNo: TThostFtdcDepositSeqNoType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryHisOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryHisOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrMiniMarginField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub MinMargin: TThostFtdcMoneyType,
    pub ValueMethod: TThostFtdcValueMethodType,
    pub IsRelative: TThostFtdcBoolType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrMiniMarginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrMarginAdjustField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub SShortMarginRatioByMoney: TThostFtdcRatioType,
    pub SShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub HShortMarginRatioByMoney: TThostFtdcRatioType,
    pub HShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub AShortMarginRatioByMoney: TThostFtdcRatioType,
    pub AShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub MShortMarginRatioByMoney: TThostFtdcRatioType,
    pub MShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrMarginAdjustField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrCommRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub StrikeRatioByMoney: TThostFtdcRatioType,
    pub StrikeRatioByVolume: TThostFtdcRatioType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrTradeCostField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub FixedMargin: TThostFtdcMoneyType,
    pub MiniMargin: TThostFtdcMoneyType,
    pub Royalty: TThostFtdcMoneyType,
    pub ExchFixedMargin: TThostFtdcMoneyType,
    pub ExchMiniMargin: TThostFtdcMoneyType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrTradeCostField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOptionInstrTradeCostField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub InputPrice: TThostFtdcPriceType,
    pub UnderlyingPrice: TThostFtdcPriceType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryOptionInstrTradeCostField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOptionInstrCommRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryOptionInstrCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcIndexPriceField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ClosePrice: TThostFtdcPriceType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcIndexPriceField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputExecOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionType: TThostFtdcActionTypeType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub ReservePositionFlag: TThostFtdcExecOrderPositionFlagType,
    pub CloseFlag: TThostFtdcExecOrderCloseFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputExecOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExecOrderActionRef: TThostFtdcOrderActionRefType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputExecOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExecOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionType: TThostFtdcActionTypeType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub ReservePositionFlag: TThostFtdcExecOrderPositionFlagType,
    pub CloseFlag: TThostFtdcExecOrderCloseFlagType,
    pub ExecOrderLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ExecResult: TThostFtdcExecResultType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerExecOrderSeq: TThostFtdcSequenceNoType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExecOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExecOrderActionRef: TThostFtdcOrderActionRefType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ExecOrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub ActionType: TThostFtdcActionTypeType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExecOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExecOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeExecOrderField {
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionType: TThostFtdcActionTypeType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub ReservePositionFlag: TThostFtdcExecOrderPositionFlagType,
    pub CloseFlag: TThostFtdcExecOrderCloseFlagType,
    pub ExecOrderLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ExecResult: TThostFtdcExecResultType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeExecOrderField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryExchangeExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExecOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeExecOrderActionField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ExecOrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub ActionType: TThostFtdcActionTypeType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub Volume: TThostFtdcVolumeType,
    pub IPAddress: TThostFtdcIPAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcExchangeExecOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeExecOrderActionField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcErrExecOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionType: TThostFtdcActionTypeType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub ReservePositionFlag: TThostFtdcExecOrderPositionFlagType,
    pub CloseFlag: TThostFtdcExecOrderCloseFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcErrExecOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryErrExecOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcErrExecOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExecOrderActionRef: TThostFtdcOrderActionRefType,
    pub ExecOrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExecOrderSysID: TThostFtdcExecOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcErrExecOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryErrExecOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrTradingRightField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Direction: TThostFtdcDirectionType,
    pub TradingRight: TThostFtdcTradingRightType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrTradingRightField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOptionInstrTradingRightField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryOptionInstrTradingRightField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputForQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ForQuoteRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputForQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcForQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ForQuoteRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub ForQuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub ForQuoteStatus: TThostFtdcForQuoteStatusType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerForQutoSeq: TThostFtdcSequenceNoType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcForQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryForQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryForQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeForQuoteField {
    pub ForQuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub ForQuoteStatus: TThostFtdcForQuoteStatusType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeForQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeForQuoteField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryExchangeForQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub QuoteRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub AskPrice: TThostFtdcPriceType,
    pub BidPrice: TThostFtdcPriceType,
    pub AskVolume: TThostFtdcVolumeType,
    pub BidVolume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub AskOffsetFlag: TThostFtdcOffsetFlagType,
    pub BidOffsetFlag: TThostFtdcOffsetFlagType,
    pub AskHedgeFlag: TThostFtdcHedgeFlagType,
    pub BidHedgeFlag: TThostFtdcHedgeFlagType,
    pub AskOrderRef: TThostFtdcOrderRefType,
    pub BidOrderRef: TThostFtdcOrderRefType,
    pub ForQuoteSysID: TThostFtdcOrderSysIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
    pub ReplaceSysID: TThostFtdcOrderSysIDType,
}
impl Default for CThostFtdcInputQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputQuoteActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub QuoteActionRef: TThostFtdcOrderActionRefType,
    pub QuoteRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputQuoteActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub QuoteRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub AskPrice: TThostFtdcPriceType,
    pub BidPrice: TThostFtdcPriceType,
    pub AskVolume: TThostFtdcVolumeType,
    pub BidVolume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub AskOffsetFlag: TThostFtdcOffsetFlagType,
    pub BidOffsetFlag: TThostFtdcOffsetFlagType,
    pub AskHedgeFlag: TThostFtdcHedgeFlagType,
    pub BidHedgeFlag: TThostFtdcHedgeFlagType,
    pub QuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub QuoteStatus: TThostFtdcOrderStatusType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub AskOrderSysID: TThostFtdcOrderSysIDType,
    pub BidOrderSysID: TThostFtdcOrderSysIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerQuoteSeq: TThostFtdcSequenceNoType,
    pub AskOrderRef: TThostFtdcOrderRefType,
    pub BidOrderRef: TThostFtdcOrderRefType,
    pub ForQuoteSysID: TThostFtdcOrderSysIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
    pub ReplaceSysID: TThostFtdcOrderSysIDType,
}
impl Default for CThostFtdcQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQuoteActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub QuoteActionRef: TThostFtdcOrderActionRefType,
    pub QuoteRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub QuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcQuoteActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryQuoteField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeQuoteField {
    pub AskPrice: TThostFtdcPriceType,
    pub BidPrice: TThostFtdcPriceType,
    pub AskVolume: TThostFtdcVolumeType,
    pub BidVolume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub AskOffsetFlag: TThostFtdcOffsetFlagType,
    pub BidOffsetFlag: TThostFtdcOffsetFlagType,
    pub AskHedgeFlag: TThostFtdcHedgeFlagType,
    pub BidHedgeFlag: TThostFtdcHedgeFlagType,
    pub QuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub QuoteStatus: TThostFtdcOrderStatusType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub AskOrderSysID: TThostFtdcOrderSysIDType,
    pub BidOrderSysID: TThostFtdcOrderSysIDType,
    pub ForQuoteSysID: TThostFtdcOrderSysIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeQuoteField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryExchangeQuoteField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryQuoteActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeQuoteActionField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub QuoteSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub QuoteLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeQuoteActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeQuoteActionField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionInstrDeltaField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Delta: TThostFtdcRatioType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcOptionInstrDeltaField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcForQuoteRspField {
    pub TradingDay: TThostFtdcDateType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ForQuoteSysID: TThostFtdcOrderSysIDType,
    pub ForQuoteTime: TThostFtdcTimeType,
    pub ActionDay: TThostFtdcDateType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcForQuoteRspField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcStrikeOffsetField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Offset: TThostFtdcMoneyType,
    pub OffsetType: TThostFtdcStrikeOffsetTypeType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcStrikeOffsetField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryStrikeOffsetField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryStrikeOffsetField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputBatchOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub UserID: TThostFtdcUserIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputBatchOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBatchOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcBatchOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeBatchOrderActionField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeBatchOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBatchOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCombInstrumentGuardField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub GuarantRatio: TThostFtdcRatioType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcCombInstrumentGuardField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCombInstrumentGuardField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryCombInstrumentGuardField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputCombActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub CombActionRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Direction: TThostFtdcDirectionType,
    pub Volume: TThostFtdcVolumeType,
    pub CombDirection: TThostFtdcCombDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputCombActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCombActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub CombActionRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Direction: TThostFtdcDirectionType,
    pub Volume: TThostFtdcVolumeType,
    pub CombDirection: TThostFtdcCombDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ActionStatus: TThostFtdcOrderActionStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ComTradeID: TThostFtdcTradeIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcCombActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCombActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryCombActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeCombActionField {
    pub Direction: TThostFtdcDirectionType,
    pub Volume: TThostFtdcVolumeType,
    pub CombDirection: TThostFtdcCombDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub ActionStatus: TThostFtdcOrderActionStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ComTradeID: TThostFtdcTradeIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeCombActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeCombActionField {
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryExchangeCombActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcProductExchRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub QuoteCurrencyID: TThostFtdcCurrencyIDType,
    pub ExchangeRate: TThostFtdcExchangeRateType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcProductExchRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryProductExchRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryProductExchRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryForQuoteParamField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryForQuoteParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcForQuoteParamField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub LastPrice: TThostFtdcPriceType,
    pub PriceInterval: TThostFtdcPriceType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcForQuoteParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMMOptionInstrCommRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub StrikeRatioByMoney: TThostFtdcRatioType,
    pub StrikeRatioByVolume: TThostFtdcRatioType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcMMOptionInstrCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMMOptionInstrCommRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryMMOptionInstrCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMMInstrumentCommissionRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcMMInstrumentCommissionRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMMInstrumentCommissionRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryMMInstrumentCommissionRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentOrderCommRateField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub OrderCommByVolume: TThostFtdcRatioType,
    pub OrderActionCommByVolume: TThostFtdcRatioType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub OrderCommByTrade: TThostFtdcRatioType,
    pub OrderActionCommByTrade: TThostFtdcRatioType,
}
impl Default for CThostFtdcInstrumentOrderCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentOrderCommRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInstrumentOrderCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradeParamField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub TradeParamID: TThostFtdcTradeParamIDType,
    pub TradeParamValue: TThostFtdcSettlementParamValueType,
    pub Memo: TThostFtdcMemoType,
}
impl Default for CThostFtdcTradeParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentMarginRateULField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentMarginRateULField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcFutureLimitPosiParamField {
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub SpecOpenVolume: TThostFtdcVolumeType,
    pub ArbiOpenVolume: TThostFtdcVolumeType,
    pub OpenVolume: TThostFtdcVolumeType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcFutureLimitPosiParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLoginForbiddenIPField {
    pub reserve1: TThostFtdcOldIPAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcLoginForbiddenIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcIPListField {
    pub reserve1: TThostFtdcOldIPAddressType,
    pub IsWhite: TThostFtdcBoolType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcIPListField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputOptionSelfCloseField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OptionSelfCloseRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub OptSelfCloseFlag: TThostFtdcOptSelfCloseFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputOptionSelfCloseField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInputOptionSelfCloseActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OptionSelfCloseActionRef: TThostFtdcOrderActionRefType,
    pub OptionSelfCloseRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcInputOptionSelfCloseActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionSelfCloseField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OptionSelfCloseRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub OptSelfCloseFlag: TThostFtdcOptSelfCloseFlagType,
    pub OptionSelfCloseLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ExecResult: TThostFtdcExecResultType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerOptionSelfCloseSeq: TThostFtdcSequenceNoType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcOptionSelfCloseField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOptionSelfCloseActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OptionSelfCloseActionRef: TThostFtdcOrderActionRefType,
    pub OptionSelfCloseRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OptionSelfCloseLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcOptionSelfCloseActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOptionSelfCloseField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub InsertTimeStart: TThostFtdcTimeType,
    pub InsertTimeEnd: TThostFtdcTimeType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryOptionSelfCloseField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOptionSelfCloseField {
    pub Volume: TThostFtdcVolumeType,
    pub RequestID: TThostFtdcRequestIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub OptSelfCloseFlag: TThostFtdcOptSelfCloseFlagType,
    pub OptionSelfCloseLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ExecResult: TThostFtdcExecResultType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcExchangeOptionSelfCloseField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryOptionSelfCloseActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeOptionSelfCloseActionField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OptionSelfCloseSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OptionSelfCloseLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub OptSelfCloseFlag: TThostFtdcOptSelfCloseFlagType,
    pub IPAddress: TThostFtdcIPAddressType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcExchangeOptionSelfCloseActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDelaySwapField {
    pub DelaySwapSeqNo: TThostFtdcDepositSeqNoType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub FromCurrencyID: TThostFtdcCurrencyIDType,
    pub FromAmount: TThostFtdcMoneyType,
    pub FromFrozenSwap: TThostFtdcMoneyType,
    pub FromRemainSwap: TThostFtdcMoneyType,
    pub ToCurrencyID: TThostFtdcCurrencyIDType,
    pub ToAmount: TThostFtdcMoneyType,
    pub IsManualSwap: TThostFtdcBoolType,
    pub IsAllRemainSetZero: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySyncDelaySwapField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub DelaySwapSeqNo: TThostFtdcDepositSeqNoType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestUnitField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InvestorUnitName: TThostFtdcPartyNameType,
    pub InvestorGroupID: TThostFtdcInvestorIDType,
    pub CommModelID: TThostFtdcInvestorIDType,
    pub MarginModelID: TThostFtdcInvestorIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcInvestUnitField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestUnitField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSecAgentCheckModeField {
    pub InvestorID: TThostFtdcInvestorIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub BrokerSecAgentID: TThostFtdcAccountIDType,
    pub CheckSelfAccount: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSecAgentTradeInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerSecAgentID: TThostFtdcAccountIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcSecAgentTradeInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataField {
    pub TradingDay: TThostFtdcDateType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub LastPrice: TThostFtdcPriceType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub PreClosePrice: TThostFtdcPriceType,
    pub PreOpenInterest: TThostFtdcLargeVolumeType,
    pub OpenPrice: TThostFtdcPriceType,
    pub HighestPrice: TThostFtdcPriceType,
    pub LowestPrice: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub Turnover: TThostFtdcMoneyType,
    pub OpenInterest: TThostFtdcLargeVolumeType,
    pub ClosePrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub UpperLimitPrice: TThostFtdcPriceType,
    pub LowerLimitPrice: TThostFtdcPriceType,
    pub PreDelta: TThostFtdcRatioType,
    pub CurrDelta: TThostFtdcRatioType,
    pub UpdateTime: TThostFtdcTimeType,
    pub UpdateMillisec: TThostFtdcMillisecType,
    pub ActionDay: TThostFtdcDateType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcMarketDataField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataBaseField {
    pub TradingDay: TThostFtdcDateType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub PreClosePrice: TThostFtdcPriceType,
    pub PreOpenInterest: TThostFtdcLargeVolumeType,
    pub PreDelta: TThostFtdcRatioType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataStaticField {
    pub OpenPrice: TThostFtdcPriceType,
    pub HighestPrice: TThostFtdcPriceType,
    pub LowestPrice: TThostFtdcPriceType,
    pub ClosePrice: TThostFtdcPriceType,
    pub UpperLimitPrice: TThostFtdcPriceType,
    pub LowerLimitPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub CurrDelta: TThostFtdcRatioType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataLastMatchField {
    pub LastPrice: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub Turnover: TThostFtdcMoneyType,
    pub OpenInterest: TThostFtdcLargeVolumeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataBestPriceField {
    pub BidPrice1: TThostFtdcPriceType,
    pub BidVolume1: TThostFtdcVolumeType,
    pub AskPrice1: TThostFtdcPriceType,
    pub AskVolume1: TThostFtdcVolumeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataBid23Field {
    pub BidPrice2: TThostFtdcPriceType,
    pub BidVolume2: TThostFtdcVolumeType,
    pub BidPrice3: TThostFtdcPriceType,
    pub BidVolume3: TThostFtdcVolumeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataAsk23Field {
    pub AskPrice2: TThostFtdcPriceType,
    pub AskVolume2: TThostFtdcVolumeType,
    pub AskPrice3: TThostFtdcPriceType,
    pub AskVolume3: TThostFtdcVolumeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataBid45Field {
    pub BidPrice4: TThostFtdcPriceType,
    pub BidVolume4: TThostFtdcVolumeType,
    pub BidPrice5: TThostFtdcPriceType,
    pub BidVolume5: TThostFtdcVolumeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataAsk45Field {
    pub AskPrice4: TThostFtdcPriceType,
    pub AskVolume4: TThostFtdcVolumeType,
    pub AskPrice5: TThostFtdcPriceType,
    pub AskVolume5: TThostFtdcVolumeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataUpdateTimeField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub UpdateTime: TThostFtdcTimeType,
    pub UpdateMillisec: TThostFtdcMillisecType,
    pub ActionDay: TThostFtdcDateType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcMarketDataUpdateTimeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataBandingPriceField {
    pub BandingUpperPrice: TThostFtdcPriceType,
    pub BandingLowerPrice: TThostFtdcPriceType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataExchangeField {
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSpecificInstrumentField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcSpecificInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInstrumentStatusField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub SettlementGroupID: TThostFtdcSettlementGroupIDType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub InstrumentStatus: TThostFtdcInstrumentStatusType,
    pub TradingSegmentSN: TThostFtdcTradingSegmentSNType,
    pub EnterTime: TThostFtdcTimeType,
    pub EnterReason: TThostFtdcInstStatusEnterReasonType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInstrumentStatusField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInstrumentStatusField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldExchangeInstIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
}
impl Default for CThostFtdcQryInstrumentStatusField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorAccountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcPositionProfitAlgorithmField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Algorithm: TThostFtdcAlgorithmType,
    pub Memo: TThostFtdcMemoType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcPositionProfitAlgorithmField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDiscountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Discount: TThostFtdcRatioType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTransferBankField {
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferBankField {
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
    pub BankName: TThostFtdcBankNameType,
    pub IsActive: TThostFtdcBoolType,
}
impl Default for CThostFtdcTransferBankField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorPositionDetailField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInvestorPositionDetailField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorPositionDetailField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Direction: TThostFtdcDirectionType,
    pub OpenDate: TThostFtdcDateType,
    pub TradeID: TThostFtdcTradeIDType,
    pub Volume: TThostFtdcVolumeType,
    pub OpenPrice: TThostFtdcPriceType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub TradeType: TThostFtdcTradeTypeType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub CloseProfitByDate: TThostFtdcMoneyType,
    pub CloseProfitByTrade: TThostFtdcMoneyType,
    pub PositionProfitByDate: TThostFtdcMoneyType,
    pub PositionProfitByTrade: TThostFtdcMoneyType,
    pub Margin: TThostFtdcMoneyType,
    pub ExchMargin: TThostFtdcMoneyType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub LastSettlementPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub CloseVolume: TThostFtdcVolumeType,
    pub CloseAmount: TThostFtdcMoneyType,
    pub TimeFirstVolume: TThostFtdcVolumeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub SpecPosiType: TThostFtdcSpecPosiTypeType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInvestorPositionDetailField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingAccountPasswordField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcTradingAccountPasswordField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMDTraderOfferField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub TraderConnectStatus: TThostFtdcTraderConnectStatusType,
    pub ConnectRequestDate: TThostFtdcDateType,
    pub ConnectRequestTime: TThostFtdcTimeType,
    pub LastReportDate: TThostFtdcDateType,
    pub LastReportTime: TThostFtdcTimeType,
    pub ConnectDate: TThostFtdcDateType,
    pub ConnectTime: TThostFtdcTimeType,
    pub StartDate: TThostFtdcDateType,
    pub StartTime: TThostFtdcTimeType,
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub MaxTradeID: TThostFtdcTradeIDType,
    pub MaxOrderMessageReference: TThostFtdcReturnCodeType,
    pub OrderCancelAlg: TThostFtdcOrderCancelAlgType,
}
impl Default for CThostFtdcMDTraderOfferField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMDTraderOfferField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub TraderID: TThostFtdcTraderIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryNoticeField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcNoticeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub Content: TThostFtdcContentType,
    pub SequenceLabel: TThostFtdcSequenceLabelType,
}
impl Default for CThostFtdcNoticeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserRightField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserRightType: TThostFtdcUserRightTypeType,
    pub IsForbidden: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySettlementInfoConfirmField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLoadSettlementInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerWithdrawAlgorithmField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub WithdrawAlgorithm: TThostFtdcAlgorithmType,
    pub UsingRatio: TThostFtdcRatioType,
    pub IncludeCloseProfit: TThostFtdcIncludeCloseProfitType,
    pub AllWithoutTrade: TThostFtdcAllWithoutTradeType,
    pub AvailIncludeCloseProfit: TThostFtdcIncludeCloseProfitType,
    pub IsBrokerUserEvent: TThostFtdcBoolType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub FundMortgageRatio: TThostFtdcRatioType,
    pub BalanceAlgorithm: TThostFtdcBalanceAlgorithmType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingAccountPasswordUpdateV1Field {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OldPassword: TThostFtdcPasswordType,
    pub NewPassword: TThostFtdcPasswordType,
}
impl Default for CThostFtdcTradingAccountPasswordUpdateV1Field {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingAccountPasswordUpdateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub OldPassword: TThostFtdcPasswordType,
    pub NewPassword: TThostFtdcPasswordType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcTradingAccountPasswordUpdateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCombinationLegField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub LegID: TThostFtdcLegIDType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
    pub LegInstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryCombinationLegField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySyncStatusField {
    pub TradingDay: TThostFtdcDateType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCombinationLegField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub LegID: TThostFtdcLegIDType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub LegMultiple: TThostFtdcLegMultipleType,
    pub ImplyLevel: TThostFtdcImplyLevelType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
    pub LegInstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcCombinationLegField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncStatusField {
    pub TradingDay: TThostFtdcDateType,
    pub DataSyncStatus: TThostFtdcDataSyncStatusType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryLinkManField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLinkManField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub PersonType: TThostFtdcPersonTypeType,
    pub IdentifiedCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub PersonName: TThostFtdcPartyNameType,
    pub Telephone: TThostFtdcTelephoneType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Priority: TThostFtdcPriorityType,
    pub UOAZipCode: TThostFtdcUOAZipCodeType,
    pub PersonFullName: TThostFtdcInvestorFullNameType,
}
impl Default for CThostFtdcLinkManField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerUserEventField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserEventType: TThostFtdcUserEventTypeType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserEventField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub UserEventType: TThostFtdcUserEventTypeType,
    pub EventSequenceNo: TThostFtdcSequenceNoType,
    pub EventDate: TThostFtdcDateType,
    pub EventTime: TThostFtdcTimeType,
    pub UserEventInfo: TThostFtdcUserEventInfoType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcBrokerUserEventField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryContractBankField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcContractBankField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBrchID: TThostFtdcBankBrchIDType,
    pub BankName: TThostFtdcBankNameType,
}
impl Default for CThostFtdcContractBankField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorPositionCombineDetailField {
    pub TradingDay: TThostFtdcDateType,
    pub OpenDate: TThostFtdcDateType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ComTradeID: TThostFtdcTradeIDType,
    pub TradeID: TThostFtdcTradeIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Direction: TThostFtdcDirectionType,
    pub TotalAmt: TThostFtdcVolumeType,
    pub Margin: TThostFtdcMoneyType,
    pub ExchMargin: TThostFtdcMoneyType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub LegID: TThostFtdcLegIDType,
    pub LegMultiple: TThostFtdcLegMultipleType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub TradeGroupID: TThostFtdcTradeGroupIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInvestorPositionCombineDetailField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcParkedOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub UserForceClose: TThostFtdcBoolType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParkedOrderID: TThostFtdcParkedOrderIDType,
    pub UserType: TThostFtdcUserTypeType,
    pub Status: TThostFtdcParkedOrderStatusType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub IsSwapOrder: TThostFtdcBoolType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcParkedOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcParkedOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeChange: TThostFtdcVolumeType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ParkedOrderActionID: TThostFtdcParkedOrderActionIDType,
    pub UserType: TThostFtdcUserTypeType,
    pub Status: TThostFtdcParkedOrderStatusType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcParkedOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryParkedOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryParkedOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryParkedOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryParkedOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRemoveParkedOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ParkedOrderID: TThostFtdcParkedOrderIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRemoveParkedOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ParkedOrderActionID: TThostFtdcParkedOrderActionIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorWithdrawAlgorithmField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub UsingRatio: TThostFtdcRatioType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub FundMortgageRatio: TThostFtdcRatioType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorPositionCombineDetailField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInvestorPositionCombineDetailField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarketDataAveragePriceField {
    pub AveragePrice: TThostFtdcPriceType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcVerifyInvestorPasswordField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Password: TThostFtdcPasswordType,
}
impl Default for CThostFtdcVerifyInvestorPasswordField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserIPField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
    pub IPMask: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcUserIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingNoticeInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub SendTime: TThostFtdcTimeType,
    pub FieldContent: TThostFtdcContentType,
    pub SequenceSeries: TThostFtdcSequenceSeriesType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
impl Default for CThostFtdcTradingNoticeInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingNoticeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub SequenceSeries: TThostFtdcSequenceSeriesType,
    pub UserID: TThostFtdcUserIDType,
    pub SendTime: TThostFtdcTimeType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FieldContent: TThostFtdcContentType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
impl Default for CThostFtdcTradingNoticeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTradingNoticeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryErrOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcErrOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub UserForceClose: TThostFtdcBoolType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub IsSwapOrder: TThostFtdcBoolType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcErrOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcErrorConditionalOrderField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub UserID: TThostFtdcUserIDType,
    pub OrderPriceType: TThostFtdcOrderPriceTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub CombOffsetFlag: TThostFtdcCombOffsetFlagType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeTotalOriginal: TThostFtdcVolumeType,
    pub TimeCondition: TThostFtdcTimeConditionType,
    pub GTDDate: TThostFtdcDateType,
    pub VolumeCondition: TThostFtdcVolumeConditionType,
    pub MinVolume: TThostFtdcVolumeType,
    pub ContingentCondition: TThostFtdcContingentConditionType,
    pub StopPrice: TThostFtdcPriceType,
    pub ForceCloseReason: TThostFtdcForceCloseReasonType,
    pub IsAutoSuspend: TThostFtdcBoolType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub RequestID: TThostFtdcRequestIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub reserve2: TThostFtdcOldExchangeInstIDType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderSubmitStatus: TThostFtdcOrderSubmitStatusType,
    pub NotifySequence: TThostFtdcSequenceNoType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub OrderSource: TThostFtdcOrderSourceType,
    pub OrderStatus: TThostFtdcOrderStatusType,
    pub OrderType: TThostFtdcOrderTypeType,
    pub VolumeTraded: TThostFtdcVolumeType,
    pub VolumeTotal: TThostFtdcVolumeType,
    pub InsertDate: TThostFtdcDateType,
    pub InsertTime: TThostFtdcTimeType,
    pub ActiveTime: TThostFtdcTimeType,
    pub SuspendTime: TThostFtdcTimeType,
    pub UpdateTime: TThostFtdcTimeType,
    pub CancelTime: TThostFtdcTimeType,
    pub ActiveTraderID: TThostFtdcTraderIDType,
    pub ClearingPartID: TThostFtdcParticipantIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub UserForceClose: TThostFtdcBoolType,
    pub ActiveUserID: TThostFtdcUserIDType,
    pub BrokerOrderSeq: TThostFtdcSequenceNoType,
    pub RelativeOrderSysID: TThostFtdcOrderSysIDType,
    pub ZCETotalTradedVolume: TThostFtdcVolumeType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub IsSwapOrder: TThostFtdcBoolType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub reserve3: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcErrorConditionalOrderField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryErrOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcErrOrderActionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OrderActionRef: TThostFtdcOrderActionRefType,
    pub OrderRef: TThostFtdcOrderRefType,
    pub RequestID: TThostFtdcRequestIDType,
    pub FrontID: TThostFtdcFrontIDType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub OrderSysID: TThostFtdcOrderSysIDType,
    pub ActionFlag: TThostFtdcActionFlagType,
    pub LimitPrice: TThostFtdcPriceType,
    pub VolumeChange: TThostFtdcVolumeType,
    pub ActionDate: TThostFtdcDateType,
    pub ActionTime: TThostFtdcTimeType,
    pub TraderID: TThostFtdcTraderIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub OrderLocalID: TThostFtdcOrderLocalIDType,
    pub ActionLocalID: TThostFtdcOrderLocalIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ClientID: TThostFtdcClientIDType,
    pub BusinessUnit: TThostFtdcBusinessUnitType,
    pub OrderActionStatus: TThostFtdcOrderActionStatusType,
    pub UserID: TThostFtdcUserIDType,
    pub StatusMsg: TThostFtdcErrorMsgType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BranchID: TThostFtdcBranchIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub reserve2: TThostFtdcOldIPAddressType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcErrOrderActionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryExchangeSequenceField {
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcExchangeSequenceField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub MarketStatus: TThostFtdcInstrumentStatusType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMaxOrderVolumeWithPriceField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub OffsetFlag: TThostFtdcOffsetFlagType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub MaxVolume: TThostFtdcVolumeType,
    pub Price: TThostFtdcPriceType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryMaxOrderVolumeWithPriceField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerTradingParamsField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub AccountID: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerTradingParamsField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub MarginPriceType: TThostFtdcMarginPriceTypeType,
    pub Algorithm: TThostFtdcAlgorithmType,
    pub AvailIncludeCloseProfit: TThostFtdcIncludeCloseProfitType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub OptionRoyaltyPriceType: TThostFtdcOptionRoyaltyPriceTypeType,
    pub AccountID: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBrokerTradingAlgosField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryBrokerTradingAlgosField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerTradingAlgosField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HandlePositionAlgoID: TThostFtdcHandlePositionAlgoIDType,
    pub FindMarginRateAlgoID: TThostFtdcFindMarginRateAlgoIDType,
    pub HandleTradingAccountAlgoID: TThostFtdcHandleTradingAccountAlgoIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcBrokerTradingAlgosField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQueryBrokerDepositField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerDepositField {
    pub TradingDay: TThostFtdcTradeDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub PreBalance: TThostFtdcMoneyType,
    pub CurrMargin: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub Balance: TThostFtdcMoneyType,
    pub Deposit: TThostFtdcMoneyType,
    pub Withdraw: TThostFtdcMoneyType,
    pub Available: TThostFtdcMoneyType,
    pub Reserve: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCFMMCBrokerKeyField {
    pub BrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCFMMCBrokerKeyField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub CreateDate: TThostFtdcDateType,
    pub CreateTime: TThostFtdcTimeType,
    pub KeyID: TThostFtdcSequenceNoType,
    pub CurrentKey: TThostFtdcCFMMCKeyType,
    pub KeyKind: TThostFtdcCFMMCKeyKindType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCFMMCTradingAccountKeyField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub KeyID: TThostFtdcSequenceNoType,
    pub CurrentKey: TThostFtdcCFMMCKeyType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCFMMCTradingAccountKeyField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserOTPParamField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub OTPVendorsID: TThostFtdcOTPVendorsIDType,
    pub SerialNumber: TThostFtdcSerialNumberType,
    pub AuthKey: TThostFtdcAuthKeyType,
    pub LastDrift: TThostFtdcLastDriftType,
    pub LastSuccess: TThostFtdcLastSuccessType,
    pub OTPType: TThostFtdcOTPTypeType,
}
impl Default for CThostFtdcBrokerUserOTPParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcManualSyncBrokerUserOTPField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub OTPType: TThostFtdcOTPTypeType,
    pub FirstOTP: TThostFtdcPasswordType,
    pub SecondOTP: TThostFtdcPasswordType,
}
impl Default for CThostFtdcManualSyncBrokerUserOTPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCommRateModelField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub CommModelID: TThostFtdcInvestorIDType,
    pub CommModelName: TThostFtdcCommModelNameType,
}
impl Default for CThostFtdcCommRateModelField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCommRateModelField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub CommModelID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMarginModelField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub MarginModelID: TThostFtdcInvestorIDType,
    pub MarginModelName: TThostFtdcCommModelNameType,
}
impl Default for CThostFtdcMarginModelField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMarginModelField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub MarginModelID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcEWarrantOffsetField {
    pub TradingDay: TThostFtdcTradeDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Volume: TThostFtdcVolumeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcEWarrantOffsetField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryEWarrantOffsetField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryEWarrantOffsetField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryInvestorProductGroupMarginField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub ProductGroupID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryInvestorProductGroupMarginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcInvestorProductGroupMarginField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub LongFrozenMargin: TThostFtdcMoneyType,
    pub ShortFrozenMargin: TThostFtdcMoneyType,
    pub UseMargin: TThostFtdcMoneyType,
    pub LongUseMargin: TThostFtdcMoneyType,
    pub ShortUseMargin: TThostFtdcMoneyType,
    pub ExchMargin: TThostFtdcMoneyType,
    pub LongExchMargin: TThostFtdcMoneyType,
    pub ShortExchMargin: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub OffsetAmount: TThostFtdcMoneyType,
    pub LongOffsetAmount: TThostFtdcMoneyType,
    pub ShortOffsetAmount: TThostFtdcMoneyType,
    pub ExchOffsetAmount: TThostFtdcMoneyType,
    pub LongExchOffsetAmount: TThostFtdcMoneyType,
    pub ShortExchOffsetAmount: TThostFtdcMoneyType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub ProductGroupID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcInvestorProductGroupMarginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQueryCFMMCTradingAccountTokenField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCFMMCTradingAccountTokenField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub ParticipantID: TThostFtdcParticipantIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub KeyID: TThostFtdcSequenceNoType,
    pub Token: TThostFtdcCFMMCTokenType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryProductGroupField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryProductGroupField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcProductGroupField {
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub reserve2: TThostFtdcOldInstrumentIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub ProductGroupID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcProductGroupField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBulletinField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub TradingDay: TThostFtdcDateType,
    pub BulletinID: TThostFtdcBulletinIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub NewsType: TThostFtdcNewsTypeType,
    pub NewsUrgency: TThostFtdcNewsUrgencyType,
    pub SendTime: TThostFtdcTimeType,
    pub Abstract: TThostFtdcAbstractType,
    pub ComeFrom: TThostFtdcComeFromType,
    pub Content: TThostFtdcContentType,
    pub URLLink: TThostFtdcURLLinkType,
    pub MarketID: TThostFtdcMarketIDType,
}
impl Default for CThostFtdcBulletinField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryBulletinField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub BulletinID: TThostFtdcBulletinIDType,
    pub SequenceNo: TThostFtdcSequenceNoType,
    pub NewsType: TThostFtdcNewsTypeType,
    pub NewsUrgency: TThostFtdcNewsUrgencyType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcMulticastInstrumentField {
    pub TopicID: TThostFtdcInstallIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentNo: TThostFtdcInstallIDType,
    pub CodePrice: TThostFtdcPriceType,
    pub VolumeMultiple: TThostFtdcVolumeMultipleType,
    pub PriceTick: TThostFtdcPriceType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcMulticastInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryMulticastInstrumentField {
    pub TopicID: TThostFtdcInstallIDType,
    pub reserve1: TThostFtdcOldInstrumentIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryMulticastInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAppIDAuthAssignField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AppID: TThostFtdcAppIDType,
    pub DRIdentityID: TThostFtdcDRIdentityIDType,
}
impl Default for CThostFtdcAppIDAuthAssignField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqOpenAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub CashExchangeCode: TThostFtdcCashExchangeCodeType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub TID: TThostFtdcTIDType,
    pub UserID: TThostFtdcUserIDType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqOpenAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqCancelAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub CashExchangeCode: TThostFtdcCashExchangeCodeType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub TID: TThostFtdcTIDType,
    pub UserID: TThostFtdcUserIDType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqCancelAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqChangeAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub NewBankAccount: TThostFtdcBankAccountType,
    pub NewBankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub TID: TThostFtdcTIDType,
    pub Digest: TThostFtdcDigestType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqChangeAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqTransferField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub FutureFetchAmount: TThostFtdcTradeAmountType,
    pub FeePayFlag: TThostFtdcFeePayFlagType,
    pub CustFee: TThostFtdcCustFeeType,
    pub BrokerFee: TThostFtdcFutureFeeType,
    pub Message: TThostFtdcAddInfoType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub TransferStatus: TThostFtdcTransferStatusType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqTransferField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspTransferField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub FutureFetchAmount: TThostFtdcTradeAmountType,
    pub FeePayFlag: TThostFtdcFeePayFlagType,
    pub CustFee: TThostFtdcCustFeeType,
    pub BrokerFee: TThostFtdcFutureFeeType,
    pub Message: TThostFtdcAddInfoType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub TransferStatus: TThostFtdcTransferStatusType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcRspTransferField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqRepealField {
    pub RepealTimeInterval: TThostFtdcRepealTimeIntervalType,
    pub RepealedTimes: TThostFtdcRepealedTimesType,
    pub BankRepealFlag: TThostFtdcBankRepealFlagType,
    pub BrokerRepealFlag: TThostFtdcBrokerRepealFlagType,
    pub PlateRepealSerial: TThostFtdcPlateSerialType,
    pub BankRepealSerial: TThostFtdcBankSerialType,
    pub FutureRepealSerial: TThostFtdcFutureSerialType,
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub FutureFetchAmount: TThostFtdcTradeAmountType,
    pub FeePayFlag: TThostFtdcFeePayFlagType,
    pub CustFee: TThostFtdcCustFeeType,
    pub BrokerFee: TThostFtdcFutureFeeType,
    pub Message: TThostFtdcAddInfoType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub TransferStatus: TThostFtdcTransferStatusType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqRepealField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspRepealField {
    pub RepealTimeInterval: TThostFtdcRepealTimeIntervalType,
    pub RepealedTimes: TThostFtdcRepealedTimesType,
    pub BankRepealFlag: TThostFtdcBankRepealFlagType,
    pub BrokerRepealFlag: TThostFtdcBrokerRepealFlagType,
    pub PlateRepealSerial: TThostFtdcPlateSerialType,
    pub BankRepealSerial: TThostFtdcBankSerialType,
    pub FutureRepealSerial: TThostFtdcFutureSerialType,
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub FutureFetchAmount: TThostFtdcTradeAmountType,
    pub FeePayFlag: TThostFtdcFeePayFlagType,
    pub CustFee: TThostFtdcCustFeeType,
    pub BrokerFee: TThostFtdcFutureFeeType,
    pub Message: TThostFtdcAddInfoType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub TransferStatus: TThostFtdcTransferStatusType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcRspRepealField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqQueryAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqQueryAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspQueryAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub BankUseAmount: TThostFtdcTradeAmountType,
    pub BankFetchAmount: TThostFtdcTradeAmountType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcRspQueryAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcFutureSignIOField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
}
impl Default for CThostFtdcFutureSignIOField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspFutureSignInField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub PinKey: TThostFtdcPasswordKeyType,
    pub MacKey: TThostFtdcPasswordKeyType,
}
impl Default for CThostFtdcRspFutureSignInField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqFutureSignOutField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
}
impl Default for CThostFtdcReqFutureSignOutField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspFutureSignOutField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcRspFutureSignOutField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqQueryTradeResultBySerialField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub Reference: TThostFtdcSerialType,
    pub RefrenceIssureType: TThostFtdcInstitutionTypeType,
    pub RefrenceIssure: TThostFtdcOrganCodeType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub Digest: TThostFtdcDigestType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcReqQueryTradeResultBySerialField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspQueryTradeResultBySerialField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub Reference: TThostFtdcSerialType,
    pub RefrenceIssureType: TThostFtdcInstitutionTypeType,
    pub RefrenceIssure: TThostFtdcOrganCodeType,
    pub OriginReturnCode: TThostFtdcReturnCodeType,
    pub OriginDescrInfoForReturnCode: TThostFtdcDescrInfoForReturnCodeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub Digest: TThostFtdcDigestType,
}
impl Default for CThostFtdcRspQueryTradeResultBySerialField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqDayEndFileReadyField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub FileBusinessCode: TThostFtdcFileBusinessCodeType,
    pub Digest: TThostFtdcDigestType,
}
impl Default for CThostFtdcReqDayEndFileReadyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReturnResultField {
    pub ReturnCode: TThostFtdcReturnCodeType,
    pub DescrInfoForReturnCode: TThostFtdcDescrInfoForReturnCodeType,
}
impl Default for CThostFtdcReturnResultField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcVerifyFuturePasswordField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub TID: TThostFtdcTIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcVerifyFuturePasswordField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcVerifyCustInfoField {
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcVerifyCustInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcVerifyFuturePasswordAndCustInfoField {
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcVerifyFuturePasswordAndCustInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDepositResultInformField {
    pub DepositSeqNo: TThostFtdcDepositSeqNoType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub Deposit: TThostFtdcMoneyType,
    pub RequestID: TThostFtdcRequestIDType,
    pub ReturnCode: TThostFtdcReturnCodeType,
    pub DescrInfoForReturnCode: TThostFtdcDescrInfoForReturnCodeType,
}
impl Default for CThostFtdcDepositResultInformField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqSyncKeyField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Message: TThostFtdcAddInfoType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
}
impl Default for CThostFtdcReqSyncKeyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspSyncKeyField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Message: TThostFtdcAddInfoType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcRspSyncKeyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcNotifyQueryAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub BankUseAmount: TThostFtdcTradeAmountType,
    pub BankFetchAmount: TThostFtdcTradeAmountType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcNotifyQueryAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTransferSerialField {
    pub PlateSerial: TThostFtdcPlateSerialType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradingDay: TThostFtdcDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub TradeCode: TThostFtdcTradeCodeType,
    pub SessionID: TThostFtdcSessionIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub FutureAccType: TThostFtdcFutureAccTypeType,
    pub AccountID: TThostFtdcAccountIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub FutureSerial: TThostFtdcFutureSerialType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub TradeAmount: TThostFtdcTradeAmountType,
    pub CustFee: TThostFtdcCustFeeType,
    pub BrokerFee: TThostFtdcFutureFeeType,
    pub AvailabilityFlag: TThostFtdcAvailabilityFlagType,
    pub OperatorCode: TThostFtdcOperatorCodeType,
    pub BankNewAccount: TThostFtdcBankAccountType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcTransferSerialField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryTransferSerialField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub BankID: TThostFtdcBankIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcNotifyFutureSignInField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub PinKey: TThostFtdcPasswordKeyType,
    pub MacKey: TThostFtdcPasswordKeyType,
}
impl Default for CThostFtdcNotifyFutureSignInField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcNotifyFutureSignOutField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Digest: TThostFtdcDigestType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcNotifyFutureSignOutField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcNotifySyncKeyField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub InstallID: TThostFtdcInstallIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Message: TThostFtdcAddInfoType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub OperNo: TThostFtdcOperNoType,
    pub RequestID: TThostFtdcRequestIDType,
    pub TID: TThostFtdcTIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcNotifySyncKeyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryAccountregisterField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAccountregisterField {
    pub TradeDay: TThostFtdcTradeDateType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub OpenOrDestroy: TThostFtdcOpenOrDestroyType,
    pub RegDate: TThostFtdcTradeDateType,
    pub OutDate: TThostFtdcTradeDateType,
    pub TID: TThostFtdcTIDType,
    pub CustType: TThostFtdcCustTypeType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcAccountregisterField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcOpenAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub CashExchangeCode: TThostFtdcCashExchangeCodeType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub TID: TThostFtdcTIDType,
    pub UserID: TThostFtdcUserIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcOpenAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCancelAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub CashExchangeCode: TThostFtdcCashExchangeCodeType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub DeviceID: TThostFtdcDeviceIDType,
    pub BankSecuAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankSecuAcc: TThostFtdcBankAccountType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub OperNo: TThostFtdcOperNoType,
    pub TID: TThostFtdcTIDType,
    pub UserID: TThostFtdcUserIDType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcCancelAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcChangeAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub NewBankAccount: TThostFtdcBankAccountType,
    pub NewBankPassWord: TThostFtdcPasswordType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub BankPwdFlag: TThostFtdcPwdFlagType,
    pub SecuPwdFlag: TThostFtdcPwdFlagType,
    pub TID: TThostFtdcTIDType,
    pub Digest: TThostFtdcDigestType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
    pub LongCustomerName: TThostFtdcLongIndividualNameType,
}
impl Default for CThostFtdcChangeAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSecAgentACIDMapField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub BrokerSecAgentID: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySecAgentACIDMapField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserRightsAssignField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub DRIdentityID: TThostFtdcDRIdentityIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcBrokerUserRightAssignField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub DRIdentityID: TThostFtdcDRIdentityIDType,
    pub Tradeable: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDRTransferField {
    pub OrigDRIdentityID: TThostFtdcDRIdentityIDType,
    pub DestDRIdentityID: TThostFtdcDRIdentityIDType,
    pub OrigBrokerID: TThostFtdcBrokerIDType,
    pub DestBrokerID: TThostFtdcBrokerIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcFensUserInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub LoginMode: TThostFtdcLoginModeType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCurrTransferIdentityField {
    pub IdentityID: TThostFtdcDRIdentityIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcLoginForbiddenUserField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcLoginForbiddenUserField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryLoginForbiddenUserField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcTradingAccountReserveField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Reserve: TThostFtdcMoneyType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryLoginForbiddenIPField {
    pub reserve1: TThostFtdcOldIPAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcQryLoginForbiddenIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryIPListField {
    pub reserve1: TThostFtdcOldIPAddressType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcQryIPListField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryUserRightsAssignField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReserveOpenAccountConfirmField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcLongIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub TID: TThostFtdcTIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub Password: TThostFtdcPasswordType,
    pub BankReserveOpenSeq: TThostFtdcBankSerialType,
    pub BookDate: TThostFtdcTradeDateType,
    pub BookPsw: TThostFtdcPasswordType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcReserveOpenAccountConfirmField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReserveOpenAccountField {
    pub TradeCode: TThostFtdcTradeCodeType,
    pub BankID: TThostFtdcBankIDType,
    pub BankBranchID: TThostFtdcBankBrchIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerBranchID: TThostFtdcFutureBranchIDType,
    pub TradeDate: TThostFtdcTradeDateType,
    pub TradeTime: TThostFtdcTradeTimeType,
    pub BankSerial: TThostFtdcBankSerialType,
    pub TradingDay: TThostFtdcTradeDateType,
    pub PlateSerial: TThostFtdcSerialType,
    pub LastFragment: TThostFtdcLastFragmentType,
    pub SessionID: TThostFtdcSessionIDType,
    pub CustomerName: TThostFtdcLongIndividualNameType,
    pub IdCardType: TThostFtdcIdCardTypeType,
    pub IdentifiedCardNo: TThostFtdcIdentifiedCardNoType,
    pub Gender: TThostFtdcGenderType,
    pub CountryCode: TThostFtdcCountryCodeType,
    pub CustType: TThostFtdcCustTypeType,
    pub Address: TThostFtdcAddressType,
    pub ZipCode: TThostFtdcZipCodeType,
    pub Telephone: TThostFtdcTelephoneType,
    pub MobilePhone: TThostFtdcMobilePhoneType,
    pub Fax: TThostFtdcFaxType,
    pub EMail: TThostFtdcEMailType,
    pub MoneyAccountStatus: TThostFtdcMoneyAccountStatusType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub BankPassWord: TThostFtdcPasswordType,
    pub InstallID: TThostFtdcInstallIDType,
    pub VerifyCertNoFlag: TThostFtdcYesNoIndicatorType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub Digest: TThostFtdcDigestType,
    pub BankAccType: TThostFtdcBankAccTypeType,
    pub BrokerIDByBank: TThostFtdcBankCodingForFutureType,
    pub TID: TThostFtdcTIDType,
    pub ReserveOpenAccStas: TThostFtdcReserveOpenAccStasType,
    pub ErrorID: TThostFtdcErrorIDType,
    pub ErrorMsg: TThostFtdcErrorMsgType,
}
impl Default for CThostFtdcReserveOpenAccountField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAccountPropertyField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub BankID: TThostFtdcBankIDType,
    pub BankAccount: TThostFtdcBankAccountType,
    pub OpenName: TThostFtdcInvestorFullNameType,
    pub OpenBank: TThostFtdcOpenBankType,
    pub IsActive: TThostFtdcBoolType,
    pub AccountSourceType: TThostFtdcAccountSourceTypeType,
    pub OpenDate: TThostFtdcDateType,
    pub CancelDate: TThostFtdcDateType,
    pub OperatorID: TThostFtdcOperatorIDType,
    pub OperateDate: TThostFtdcDateType,
    pub OperateTime: TThostFtdcTimeType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
}
impl Default for CThostFtdcAccountPropertyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCurrDRIdentityField {
    pub DRIdentityID: TThostFtdcDRIdentityIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCurrDRIdentityField {
    pub DRIdentityID: TThostFtdcDRIdentityIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySecAgentCheckModeField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQrySecAgentTradeInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub BrokerSecAgentID: TThostFtdcAccountIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserAuthMethodField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspUserAuthMethodField {
    pub UsableAuthMethod: TThostFtdcCurrentAuthMethodType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqGenUserCaptchaField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspGenUserCaptchaField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub CaptchaInfoLen: TThostFtdcCaptchaInfoLenType,
    pub CaptchaInfo: TThostFtdcCaptchaInfoType,
}
impl Default for CThostFtdcRspGenUserCaptchaField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqGenUserTextField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRspGenUserTextField {
    pub UserTextSeq: TThostFtdcUserTextSeqType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserLoginWithCaptchaField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub Captcha: TThostFtdcPasswordType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcReqUserLoginWithCaptchaField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserLoginWithTextField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub Text: TThostFtdcPasswordType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcReqUserLoginWithTextField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserLoginWithOTPField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub OTPPassword: TThostFtdcPasswordType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcReqUserLoginWithOTPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone)]
pub struct CThostFtdcReqApiHandshakeField {
    pub CryptoKeyVersion: TThostFtdcCryptoKeyVersionType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct CThostFtdcRspApiHandshakeField {
    pub FrontHandshakeDataLen: TThostFtdcHandshakeDataLenType,
    pub FrontHandshakeData: TThostFtdcHandshakeDataType,
    pub IsApiAuthEnabled: TThostFtdcBoolType,
}
impl Default for CThostFtdcRspApiHandshakeField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct CThostFtdcReqVerifyApiKeyField {
    pub ApiHandshakeDataLen: TThostFtdcHandshakeDataLenType,
    pub ApiHandshakeData: TThostFtdcHandshakeDataType,
}
impl Default for CThostFtdcReqVerifyApiKeyField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcDepartmentUserField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub InvestorRange: TThostFtdcDepartmentRangeType,
    pub InvestorID: TThostFtdcInvestorIDType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQueryFreqField {
    pub QueryFreq: TThostFtdcQueryFreqType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAuthForbiddenIPField {
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcAuthForbiddenIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryAuthForbiddenIPField {
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcQryAuthForbiddenIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDelaySwapFrozenField {
    pub DelaySwapSeqNo: TThostFtdcDepositSeqNoType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub FromCurrencyID: TThostFtdcCurrencyIDType,
    pub FromRemainSwap: TThostFtdcMoneyType,
    pub IsManualSwap: TThostFtdcBoolType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcUserSystemInfoField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub ClientSystemInfoLen: TThostFtdcSystemInfoLenType,
    pub ClientSystemInfo: TThostFtdcClientSystemInfoType,
    pub reserve1: TThostFtdcOldIPAddressType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub ClientLoginTime: TThostFtdcTimeType,
    pub ClientAppID: TThostFtdcAppIDType,
    pub ClientPublicIP: TThostFtdcIPAddressType,
    pub ClientLoginRemark: TThostFtdcClientLoginRemarkType,
}
impl Default for CThostFtdcUserSystemInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAuthUserIDField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AppID: TThostFtdcAppIDType,
    pub UserID: TThostFtdcUserIDType,
    pub AuthType: TThostFtdcAuthTypeType,
}
impl Default for CThostFtdcAuthUserIDField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcAuthIPField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AppID: TThostFtdcAppIDType,
    pub IPAddress: TThostFtdcIPAddressType,
}
impl Default for CThostFtdcAuthIPField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryClassifiedInstrumentField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub TradingType: TThostFtdcTradingTypeType,
    pub ClassType: TThostFtdcClassTypeType,
}
impl Default for CThostFtdcQryClassifiedInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryCombPromotionParamField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryCombPromotionParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcCombPromotionParamField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub CombHedgeFlag: TThostFtdcCombHedgeFlagType,
    pub Xparameter: TThostFtdcDiscountRatioType,
}
impl Default for CThostFtdcCombPromotionParamField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcReqUserLoginSCField {
    pub TradingDay: TThostFtdcDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub UserID: TThostFtdcUserIDType,
    pub Password: TThostFtdcPasswordType,
    pub UserProductInfo: TThostFtdcProductInfoType,
    pub InterfaceProductInfo: TThostFtdcProductInfoType,
    pub ProtocolInfo: TThostFtdcProtocolInfoType,
    pub MacAddress: TThostFtdcMacAddressType,
    pub OneTimePassword: TThostFtdcPasswordType,
    pub ClientIPAddress: TThostFtdcIPAddressType,
    pub LoginRemark: TThostFtdcLoginRemarkType,
    pub ClientIPPort: TThostFtdcIPPortType,
    pub AuthCode: TThostFtdcAuthCodeType,
    pub AppID: TThostFtdcAppIDType,
}
impl Default for CThostFtdcReqUserLoginSCField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryRiskSettleInvstPositionField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryRiskSettleInvstPositionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcQryRiskSettleProductStatusField {
    pub ProductID: TThostFtdcInstrumentIDType,
}
impl Default for CThostFtdcQryRiskSettleProductStatusField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRiskSettleInvstPositionField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub PosiDirection: TThostFtdcPosiDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub PositionDate: TThostFtdcPositionDateType,
    pub YdPosition: TThostFtdcVolumeType,
    pub Position: TThostFtdcVolumeType,
    pub LongFrozen: TThostFtdcVolumeType,
    pub ShortFrozen: TThostFtdcVolumeType,
    pub LongFrozenAmount: TThostFtdcMoneyType,
    pub ShortFrozenAmount: TThostFtdcMoneyType,
    pub OpenVolume: TThostFtdcVolumeType,
    pub CloseVolume: TThostFtdcVolumeType,
    pub OpenAmount: TThostFtdcMoneyType,
    pub CloseAmount: TThostFtdcMoneyType,
    pub PositionCost: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub UseMargin: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub OpenCost: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub CombPosition: TThostFtdcVolumeType,
    pub CombLongFrozen: TThostFtdcVolumeType,
    pub CombShortFrozen: TThostFtdcVolumeType,
    pub CloseProfitByDate: TThostFtdcMoneyType,
    pub CloseProfitByTrade: TThostFtdcMoneyType,
    pub TodayPosition: TThostFtdcVolumeType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub StrikeFrozen: TThostFtdcVolumeType,
    pub StrikeFrozenAmount: TThostFtdcMoneyType,
    pub AbandonFrozen: TThostFtdcVolumeType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub YdStrikeFrozen: TThostFtdcVolumeType,
    pub InvestUnitID: TThostFtdcInvestUnitIDType,
    pub PositionCostOffset: TThostFtdcMoneyType,
    pub TasPosition: TThostFtdcVolumeType,
    pub TasPositionCost: TThostFtdcMoneyType,
}
impl Default for CThostFtdcRiskSettleInvstPositionField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcRiskSettleProductStatusField {
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub ProductStatus: TThostFtdcProductStatusType,
}
impl Default for CThostFtdcRiskSettleProductStatusField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInfoField {
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
    pub SyncDeltaStatus: TThostFtdcSyncDeltaStatusType,
    pub SyncDescription: TThostFtdcSyncDescriptionType,
    pub IsOnlyTrdDelta: TThostFtdcBoolType,
}
impl Default for CThostFtdcSyncDeltaInfoField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaProductStatusField {
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub ProductStatus: TThostFtdcProductStatusType,
}
impl Default for CThostFtdcSyncDeltaProductStatusField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInvstPosDtlField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Direction: TThostFtdcDirectionType,
    pub OpenDate: TThostFtdcDateType,
    pub TradeID: TThostFtdcTradeIDType,
    pub Volume: TThostFtdcVolumeType,
    pub OpenPrice: TThostFtdcPriceType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub TradeType: TThostFtdcTradeTypeType,
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub CloseProfitByDate: TThostFtdcMoneyType,
    pub CloseProfitByTrade: TThostFtdcMoneyType,
    pub PositionProfitByDate: TThostFtdcMoneyType,
    pub PositionProfitByTrade: TThostFtdcMoneyType,
    pub Margin: TThostFtdcMoneyType,
    pub ExchMargin: TThostFtdcMoneyType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub LastSettlementPrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub CloseVolume: TThostFtdcVolumeType,
    pub CloseAmount: TThostFtdcMoneyType,
    pub TimeFirstVolume: TThostFtdcVolumeType,
    pub SpecPosiType: TThostFtdcSpecPosiTypeType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaInvstPosDtlField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInvstPosCombDtlField {
    pub TradingDay: TThostFtdcDateType,
    pub OpenDate: TThostFtdcDateType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ComTradeID: TThostFtdcTradeIDType,
    pub TradeID: TThostFtdcTradeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Direction: TThostFtdcDirectionType,
    pub TotalAmt: TThostFtdcVolumeType,
    pub Margin: TThostFtdcMoneyType,
    pub ExchMargin: TThostFtdcMoneyType,
    pub MarginRateByMoney: TThostFtdcRatioType,
    pub MarginRateByVolume: TThostFtdcRatioType,
    pub LegID: TThostFtdcLegIDType,
    pub LegMultiple: TThostFtdcLegMultipleType,
    pub TradeGroupID: TThostFtdcTradeGroupIDType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaInvstPosCombDtlField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaTradingAccountField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub AccountID: TThostFtdcAccountIDType,
    pub PreMortgage: TThostFtdcMoneyType,
    pub PreCredit: TThostFtdcMoneyType,
    pub PreDeposit: TThostFtdcMoneyType,
    pub PreBalance: TThostFtdcMoneyType,
    pub PreMargin: TThostFtdcMoneyType,
    pub InterestBase: TThostFtdcMoneyType,
    pub Interest: TThostFtdcMoneyType,
    pub Deposit: TThostFtdcMoneyType,
    pub Withdraw: TThostFtdcMoneyType,
    pub FrozenMargin: TThostFtdcMoneyType,
    pub FrozenCash: TThostFtdcMoneyType,
    pub FrozenCommission: TThostFtdcMoneyType,
    pub CurrMargin: TThostFtdcMoneyType,
    pub CashIn: TThostFtdcMoneyType,
    pub Commission: TThostFtdcMoneyType,
    pub CloseProfit: TThostFtdcMoneyType,
    pub PositionProfit: TThostFtdcMoneyType,
    pub Balance: TThostFtdcMoneyType,
    pub Available: TThostFtdcMoneyType,
    pub WithdrawQuota: TThostFtdcMoneyType,
    pub Reserve: TThostFtdcMoneyType,
    pub TradingDay: TThostFtdcDateType,
    pub SettlementID: TThostFtdcSettlementIDType,
    pub Credit: TThostFtdcMoneyType,
    pub Mortgage: TThostFtdcMoneyType,
    pub ExchangeMargin: TThostFtdcMoneyType,
    pub DeliveryMargin: TThostFtdcMoneyType,
    pub ExchangeDeliveryMargin: TThostFtdcMoneyType,
    pub ReserveBalance: TThostFtdcMoneyType,
    pub CurrencyID: TThostFtdcCurrencyIDType,
    pub PreFundMortgageIn: TThostFtdcMoneyType,
    pub PreFundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageIn: TThostFtdcMoneyType,
    pub FundMortgageOut: TThostFtdcMoneyType,
    pub FundMortgageAvailable: TThostFtdcMoneyType,
    pub MortgageableFund: TThostFtdcMoneyType,
    pub SpecProductMargin: TThostFtdcMoneyType,
    pub SpecProductFrozenMargin: TThostFtdcMoneyType,
    pub SpecProductCommission: TThostFtdcMoneyType,
    pub SpecProductFrozenCommission: TThostFtdcMoneyType,
    pub SpecProductPositionProfit: TThostFtdcMoneyType,
    pub SpecProductCloseProfit: TThostFtdcMoneyType,
    pub SpecProductPositionProfitByAlg: TThostFtdcMoneyType,
    pub SpecProductExchangeMargin: TThostFtdcMoneyType,
    pub FrozenSwap: TThostFtdcMoneyType,
    pub RemainSwap: TThostFtdcMoneyType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
#[repr(C)]
#[derive(Debug, Default, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInitInvstMarginField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub LastRiskTotalInvstMargin: TThostFtdcMoneyType,
    pub LastRiskTotalExchMargin: TThostFtdcMoneyType,
    pub ThisSyncInvstMargin: TThostFtdcMoneyType,
    pub ThisSyncExchMargin: TThostFtdcMoneyType,
    pub RemainRiskInvstMargin: TThostFtdcMoneyType,
    pub RemainRiskExchMargin: TThostFtdcMoneyType,
    pub LastRiskSpecTotalInvstMargin: TThostFtdcMoneyType,
    pub LastRiskSpecTotalExchMargin: TThostFtdcMoneyType,
    pub ThisSyncSpecInvstMargin: TThostFtdcMoneyType,
    pub ThisSyncSpecExchMargin: TThostFtdcMoneyType,
    pub RemainRiskSpecInvstMargin: TThostFtdcMoneyType,
    pub RemainRiskSpecExchMargin: TThostFtdcMoneyType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaDceCombInstrumentField {
    pub CombInstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub TradeGroupID: TThostFtdcTradeGroupIDType,
    pub CombHedgeFlag: TThostFtdcHedgeFlagType,
    pub CombinationType: TThostFtdcDceCombinationTypeType,
    pub Direction: TThostFtdcDirectionType,
    pub ProductID: TThostFtdcInstrumentIDType,
    pub Xparameter: TThostFtdcDiscountRatioType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaDceCombInstrumentField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInvstMarginRateField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaInvstMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaExchMarginRateField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaExchMarginRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaOptExchMarginField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub SShortMarginRatioByMoney: TThostFtdcRatioType,
    pub SShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub HShortMarginRatioByMoney: TThostFtdcRatioType,
    pub HShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub AShortMarginRatioByMoney: TThostFtdcRatioType,
    pub AShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub MShortMarginRatioByMoney: TThostFtdcRatioType,
    pub MShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaOptExchMarginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaOptInvstMarginField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub SShortMarginRatioByMoney: TThostFtdcRatioType,
    pub SShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub HShortMarginRatioByMoney: TThostFtdcRatioType,
    pub HShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub AShortMarginRatioByMoney: TThostFtdcRatioType,
    pub AShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub IsRelative: TThostFtdcBoolType,
    pub MShortMarginRatioByMoney: TThostFtdcRatioType,
    pub MShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaOptInvstMarginField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInvstMarginRateULField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub LongMarginRatioByMoney: TThostFtdcRatioType,
    pub LongMarginRatioByVolume: TThostFtdcMoneyType,
    pub ShortMarginRatioByMoney: TThostFtdcRatioType,
    pub ShortMarginRatioByVolume: TThostFtdcMoneyType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaInvstMarginRateULField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaOptInvstCommRateField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub StrikeRatioByMoney: TThostFtdcRatioType,
    pub StrikeRatioByVolume: TThostFtdcRatioType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaOptInvstCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaInvstCommRateField {
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub InvestorRange: TThostFtdcInvestorRangeType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub OpenRatioByMoney: TThostFtdcRatioType,
    pub OpenRatioByVolume: TThostFtdcRatioType,
    pub CloseRatioByMoney: TThostFtdcRatioType,
    pub CloseRatioByVolume: TThostFtdcRatioType,
    pub CloseTodayRatioByMoney: TThostFtdcRatioType,
    pub CloseTodayRatioByVolume: TThostFtdcRatioType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaInvstCommRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaProductExchRateField {
    pub ProductID: TThostFtdcInstrumentIDType,
    pub QuoteCurrencyID: TThostFtdcCurrencyIDType,
    pub ExchangeRate: TThostFtdcExchangeRateType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaProductExchRateField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaDepthMarketDataField {
    pub TradingDay: TThostFtdcDateType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub ExchangeInstID: TThostFtdcExchangeInstIDType,
    pub LastPrice: TThostFtdcPriceType,
    pub PreSettlementPrice: TThostFtdcPriceType,
    pub PreClosePrice: TThostFtdcPriceType,
    pub PreOpenInterest: TThostFtdcLargeVolumeType,
    pub OpenPrice: TThostFtdcPriceType,
    pub HighestPrice: TThostFtdcPriceType,
    pub LowestPrice: TThostFtdcPriceType,
    pub Volume: TThostFtdcVolumeType,
    pub Turnover: TThostFtdcMoneyType,
    pub OpenInterest: TThostFtdcLargeVolumeType,
    pub ClosePrice: TThostFtdcPriceType,
    pub SettlementPrice: TThostFtdcPriceType,
    pub UpperLimitPrice: TThostFtdcPriceType,
    pub LowerLimitPrice: TThostFtdcPriceType,
    pub PreDelta: TThostFtdcRatioType,
    pub CurrDelta: TThostFtdcRatioType,
    pub UpdateTime: TThostFtdcTimeType,
    pub UpdateMillisec: TThostFtdcMillisecType,
    pub BidPrice1: TThostFtdcPriceType,
    pub BidVolume1: TThostFtdcVolumeType,
    pub AskPrice1: TThostFtdcPriceType,
    pub AskVolume1: TThostFtdcVolumeType,
    pub BidPrice2: TThostFtdcPriceType,
    pub BidVolume2: TThostFtdcVolumeType,
    pub AskPrice2: TThostFtdcPriceType,
    pub AskVolume2: TThostFtdcVolumeType,
    pub BidPrice3: TThostFtdcPriceType,
    pub BidVolume3: TThostFtdcVolumeType,
    pub AskPrice3: TThostFtdcPriceType,
    pub AskVolume3: TThostFtdcVolumeType,
    pub BidPrice4: TThostFtdcPriceType,
    pub BidVolume4: TThostFtdcVolumeType,
    pub AskPrice4: TThostFtdcPriceType,
    pub AskVolume4: TThostFtdcVolumeType,
    pub BidPrice5: TThostFtdcPriceType,
    pub BidVolume5: TThostFtdcVolumeType,
    pub AskPrice5: TThostFtdcPriceType,
    pub AskVolume5: TThostFtdcVolumeType,
    pub AveragePrice: TThostFtdcPriceType,
    pub ActionDay: TThostFtdcDateType,
    pub BandingUpperPrice: TThostFtdcPriceType,
    pub BandingLowerPrice: TThostFtdcPriceType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaDepthMarketDataField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaIndexPriceField {
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub ClosePrice: TThostFtdcPriceType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaIndexPriceField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
#[derive(Debug, Copy, Clone, Decode, Encode)]
pub struct CThostFtdcSyncDeltaEWarrantOffsetField {
    pub TradingDay: TThostFtdcTradeDateType,
    pub BrokerID: TThostFtdcBrokerIDType,
    pub InvestorID: TThostFtdcInvestorIDType,
    pub ExchangeID: TThostFtdcExchangeIDType,
    pub InstrumentID: TThostFtdcInstrumentIDType,
    pub Direction: TThostFtdcDirectionType,
    pub HedgeFlag: TThostFtdcHedgeFlagType,
    pub Volume: TThostFtdcVolumeType,
    pub ActionDirection: TThostFtdcActionDirectionType,
    pub SyncDeltaSequenceNo: TThostFtdcSequenceNoType,
}
impl Default for CThostFtdcSyncDeltaEWarrantOffsetField {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
pub struct CThostFtdcMdSpi__bindgen_vtable {}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct CThostFtdcMdSpi {
    pub vtable_: *const CThostFtdcMdSpi__bindgen_vtable,
}
impl Default for CThostFtdcMdSpi {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
pub struct CThostFtdcMdApi__bindgen_vtable {
    pub CThostFtdcMdApi_Release: unsafe extern "C" fn(this: *mut CThostFtdcMdApi),
    pub CThostFtdcMdApi_Init: unsafe extern "C" fn(this: *mut CThostFtdcMdApi),
    pub CThostFtdcMdApi_Join:
        unsafe extern "C" fn(this: *mut CThostFtdcMdApi) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_GetTradingDay:
        unsafe extern "C" fn(this: *mut CThostFtdcMdApi) -> *const ::std::os::raw::c_char,
    pub CThostFtdcMdApi_RegisterFront: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        pszFrontAddress: *mut ::std::os::raw::c_char,
    ),
    pub CThostFtdcMdApi_RegisterNameServer:
        unsafe extern "C" fn(this: *mut CThostFtdcMdApi, pszNsAddress: *mut ::std::os::raw::c_char),
    pub CThostFtdcMdApi_RegisterFensUserInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        pFensUserInfo: *mut CThostFtdcFensUserInfoField,
    ),
    pub CThostFtdcMdApi_RegisterSpi:
        unsafe extern "C" fn(this: *mut CThostFtdcMdApi, pSpi: *mut CThostFtdcMdSpi),
    pub CThostFtdcMdApi_SubscribeMarketData: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        ppInstrumentID: *mut *mut ::std::os::raw::c_char,
        nCount: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_UnSubscribeMarketData: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        ppInstrumentID: *mut *mut ::std::os::raw::c_char,
        nCount: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_SubscribeForQuoteRsp: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        ppInstrumentID: *mut *mut ::std::os::raw::c_char,
        nCount: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_UnSubscribeForQuoteRsp: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        ppInstrumentID: *mut *mut ::std::os::raw::c_char,
        nCount: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_ReqUserLogin: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        pReqUserLoginField: *mut CThostFtdcReqUserLoginField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_ReqUserLogout: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        pUserLogout: *mut CThostFtdcUserLogoutField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcMdApi_ReqQryMulticastInstrument: unsafe extern "C" fn(
        this: *mut CThostFtdcMdApi,
        pQryMulticastInstrument: *mut CThostFtdcQryMulticastInstrumentField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
}
#[repr(C)]
#[derive(Debug)]
pub struct CThostFtdcMdApi {
    pub vtable_: *const CThostFtdcMdApi__bindgen_vtable,
}
extern "C" {
    #[link_name = "\u{1}?CreateFtdcMdApi@CThostFtdcMdApi@@SAPEAV1@PEBD_N1@Z"]
    pub fn CThostFtdcMdApi_CreateFtdcMdApi(
        pszFlowPath: *const ::std::os::raw::c_char,
        bIsUsingUdp: bool,
        bIsMulticast: bool,
    ) -> *mut CThostFtdcMdApi;
}
extern "C" {
    #[link_name = "\u{1}?GetApiVersion@CThostFtdcMdApi@@SAPEBDXZ"]
    pub fn CThostFtdcMdApi_GetApiVersion() -> *const ::std::os::raw::c_char;
}
impl Default for CThostFtdcMdApi {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
impl CThostFtdcMdApi {
    #[inline]
    pub unsafe fn CreateFtdcMdApi(
        pszFlowPath: *const ::std::os::raw::c_char,
        bIsUsingUdp: bool,
        bIsMulticast: bool,
    ) -> *mut CThostFtdcMdApi {
        CThostFtdcMdApi_CreateFtdcMdApi(pszFlowPath, bIsUsingUdp, bIsMulticast)
    }
    #[inline]
    pub unsafe fn GetApiVersion() -> *const ::std::os::raw::c_char {
        CThostFtdcMdApi_GetApiVersion()
    }
}
#[repr(C)]
pub struct CThostFtdcTraderSpi__bindgen_vtable {}
#[repr(C)]
#[derive(Debug, Copy, Clone)]
pub struct CThostFtdcTraderSpi {
    pub vtable_: *const CThostFtdcTraderSpi__bindgen_vtable,
}
impl Default for CThostFtdcTraderSpi {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
#[repr(C)]
pub struct CThostFtdcTraderApi__bindgen_vtable {
    pub CThostFtdcTraderApi_Release: unsafe extern "C" fn(this: *mut CThostFtdcTraderApi),
    pub CThostFtdcTraderApi_Init: unsafe extern "C" fn(this: *mut CThostFtdcTraderApi),
    pub CThostFtdcTraderApi_Join:
        unsafe extern "C" fn(this: *mut CThostFtdcTraderApi) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_GetTradingDay:
        unsafe extern "C" fn(this: *mut CThostFtdcTraderApi) -> *const ::std::os::raw::c_char,
    pub CThostFtdcTraderApi_RegisterFront: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pszFrontAddress: *mut ::std::os::raw::c_char,
    ),
    pub CThostFtdcTraderApi_RegisterNameServer: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pszNsAddress: *mut ::std::os::raw::c_char,
    ),
    pub CThostFtdcTraderApi_RegisterFensUserInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pFensUserInfo: *mut CThostFtdcFensUserInfoField,
    ),
    pub CThostFtdcTraderApi_RegisterSpi:
        unsafe extern "C" fn(this: *mut CThostFtdcTraderApi, pSpi: *mut CThostFtdcTraderSpi),
    pub CThostFtdcTraderApi_SubscribePrivateTopic:
        unsafe extern "C" fn(this: *mut CThostFtdcTraderApi, nResumeType: THOST_TE_RESUME_TYPE),
    pub CThostFtdcTraderApi_SubscribePublicTopic:
        unsafe extern "C" fn(this: *mut CThostFtdcTraderApi, nResumeType: THOST_TE_RESUME_TYPE),
    pub CThostFtdcTraderApi_ReqAuthenticate: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqAuthenticateField: *mut CThostFtdcReqAuthenticateField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_RegisterUserSystemInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pUserSystemInfo: *mut CThostFtdcUserSystemInfoField,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_SubmitUserSystemInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pUserSystemInfo: *mut CThostFtdcUserSystemInfoField,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserLogin: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqUserLoginField: *mut CThostFtdcReqUserLoginField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserLogout: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pUserLogout: *mut CThostFtdcUserLogoutField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserPasswordUpdate: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pUserPasswordUpdate: *mut CThostFtdcUserPasswordUpdateField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqTradingAccountPasswordUpdate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pTradingAccountPasswordUpdate: *mut CThostFtdcTradingAccountPasswordUpdateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserAuthMethod: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqUserAuthMethod: *mut CThostFtdcReqUserAuthMethodField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqGenUserCaptcha: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqGenUserCaptcha: *mut CThostFtdcReqGenUserCaptchaField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqGenUserText: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqGenUserText: *mut CThostFtdcReqGenUserTextField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserLoginWithCaptcha: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqUserLoginWithCaptcha: *mut CThostFtdcReqUserLoginWithCaptchaField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserLoginWithText: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqUserLoginWithText: *mut CThostFtdcReqUserLoginWithTextField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqUserLoginWithOTP: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pReqUserLoginWithOTP: *mut CThostFtdcReqUserLoginWithOTPField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqOrderInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputOrder: *mut CThostFtdcInputOrderField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqParkedOrderInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pParkedOrder: *mut CThostFtdcParkedOrderField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqParkedOrderAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pParkedOrderAction: *mut CThostFtdcParkedOrderActionField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqOrderAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputOrderAction: *mut CThostFtdcInputOrderActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryMaxOrderVolume: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryMaxOrderVolume: *mut CThostFtdcQryMaxOrderVolumeField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqSettlementInfoConfirm: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pSettlementInfoConfirm: *mut CThostFtdcSettlementInfoConfirmField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqRemoveParkedOrder: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pRemoveParkedOrder: *mut CThostFtdcRemoveParkedOrderField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqRemoveParkedOrderAction:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pRemoveParkedOrderAction: *mut CThostFtdcRemoveParkedOrderActionField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqExecOrderInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputExecOrder: *mut CThostFtdcInputExecOrderField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqExecOrderAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputExecOrderAction: *mut CThostFtdcInputExecOrderActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqForQuoteInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputForQuote: *mut CThostFtdcInputForQuoteField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQuoteInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputQuote: *mut CThostFtdcInputQuoteField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQuoteAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputQuoteAction: *mut CThostFtdcInputQuoteActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqBatchOrderAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputBatchOrderAction: *mut CThostFtdcInputBatchOrderActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqOptionSelfCloseInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputOptionSelfClose: *mut CThostFtdcInputOptionSelfCloseField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqOptionSelfCloseAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputOptionSelfCloseAction: *mut CThostFtdcInputOptionSelfCloseActionField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqCombActionInsert: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pInputCombAction: *mut CThostFtdcInputCombActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryOrder: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryOrder: *mut CThostFtdcQryOrderField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTrade: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTrade: *mut CThostFtdcQryTradeField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestorPosition: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryInvestorPosition: *mut CThostFtdcQryInvestorPositionField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTradingAccount: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTradingAccount: *mut CThostFtdcQryTradingAccountField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestor: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryInvestor: *mut CThostFtdcQryInvestorField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTradingCode: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTradingCode: *mut CThostFtdcQryTradingCodeField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInstrumentMarginRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInstrumentMarginRate: *mut CThostFtdcQryInstrumentMarginRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInstrumentCommissionRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInstrumentCommissionRate: *mut CThostFtdcQryInstrumentCommissionRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryExchange: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryExchange: *mut CThostFtdcQryExchangeField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryProduct: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryProduct: *mut CThostFtdcQryProductField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInstrument: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryInstrument: *mut CThostFtdcQryInstrumentField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryDepthMarketData: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryDepthMarketData: *mut CThostFtdcQryDepthMarketDataField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTraderOffer: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTraderOffer: *mut CThostFtdcQryTraderOfferField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySettlementInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQrySettlementInfo: *mut CThostFtdcQrySettlementInfoField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTransferBank: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTransferBank: *mut CThostFtdcQryTransferBankField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestorPositionDetail:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInvestorPositionDetail: *mut CThostFtdcQryInvestorPositionDetailField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryNotice: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryNotice: *mut CThostFtdcQryNoticeField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySettlementInfoConfirm:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQrySettlementInfoConfirm: *mut CThostFtdcQrySettlementInfoConfirmField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestorPositionCombineDetail:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInvestorPositionCombineDetail: *mut CThostFtdcQryInvestorPositionCombineDetailField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryCFMMCTradingAccountKey:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryCFMMCTradingAccountKey: *mut CThostFtdcQryCFMMCTradingAccountKeyField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryEWarrantOffset: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryEWarrantOffset: *mut CThostFtdcQryEWarrantOffsetField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestorProductGroupMargin:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInvestorProductGroupMargin: *mut CThostFtdcQryInvestorProductGroupMarginField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryExchangeMarginRate: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryExchangeMarginRate: *mut CThostFtdcQryExchangeMarginRateField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryExchangeMarginRateAdjust:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryExchangeMarginRateAdjust: *mut CThostFtdcQryExchangeMarginRateAdjustField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryExchangeRate: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryExchangeRate: *mut CThostFtdcQryExchangeRateField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySecAgentACIDMap: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQrySecAgentACIDMap: *mut CThostFtdcQrySecAgentACIDMapField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryProductExchRate: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryProductExchRate: *mut CThostFtdcQryProductExchRateField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryProductGroup: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryProductGroup: *mut CThostFtdcQryProductGroupField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryMMInstrumentCommissionRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryMMInstrumentCommissionRate: *mut CThostFtdcQryMMInstrumentCommissionRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryMMOptionInstrCommRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryMMOptionInstrCommRate: *mut CThostFtdcQryMMOptionInstrCommRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInstrumentOrderCommRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryInstrumentOrderCommRate: *mut CThostFtdcQryInstrumentOrderCommRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySecAgentTradingAccount:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryTradingAccount: *mut CThostFtdcQryTradingAccountField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySecAgentCheckMode: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQrySecAgentCheckMode: *mut CThostFtdcQrySecAgentCheckModeField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQrySecAgentTradeInfo: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQrySecAgentTradeInfo: *mut CThostFtdcQrySecAgentTradeInfoField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryOptionInstrTradeCost:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryOptionInstrTradeCost: *mut CThostFtdcQryOptionInstrTradeCostField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryOptionInstrCommRate:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryOptionInstrCommRate: *mut CThostFtdcQryOptionInstrCommRateField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryExecOrder: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryExecOrder: *mut CThostFtdcQryExecOrderField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryForQuote: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryForQuote: *mut CThostFtdcQryForQuoteField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryQuote: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryQuote: *mut CThostFtdcQryQuoteField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryOptionSelfClose: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryOptionSelfClose: *mut CThostFtdcQryOptionSelfCloseField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryInvestUnit: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryInvestUnit: *mut CThostFtdcQryInvestUnitField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryCombInstrumentGuard:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryCombInstrumentGuard: *mut CThostFtdcQryCombInstrumentGuardField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryCombAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryCombAction: *mut CThostFtdcQryCombActionField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTransferSerial: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTransferSerial: *mut CThostFtdcQryTransferSerialField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryAccountregister: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryAccountregister: *mut CThostFtdcQryAccountregisterField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryContractBank: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryContractBank: *mut CThostFtdcQryContractBankField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryParkedOrder: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryParkedOrder: *mut CThostFtdcQryParkedOrderField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryParkedOrderAction: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryParkedOrderAction: *mut CThostFtdcQryParkedOrderActionField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryTradingNotice: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryTradingNotice: *mut CThostFtdcQryTradingNoticeField,
        nRequestID: ::std::os::raw::c_int,
    ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryBrokerTradingParams:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryBrokerTradingParams: *mut CThostFtdcQryBrokerTradingParamsField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryBrokerTradingAlgos: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryBrokerTradingAlgos: *mut CThostFtdcQryBrokerTradingAlgosField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQueryCFMMCTradingAccountToken:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQueryCFMMCTradingAccountToken: *mut CThostFtdcQueryCFMMCTradingAccountTokenField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqFromBankToFutureByFuture:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pReqTransfer: *mut CThostFtdcReqTransferField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqFromFutureToBankByFuture:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pReqTransfer: *mut CThostFtdcReqTransferField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQueryBankAccountMoneyByFuture:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pReqQueryAccount: *mut CThostFtdcReqQueryAccountField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryClassifiedInstrument:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryClassifiedInstrument: *mut CThostFtdcQryClassifiedInstrumentField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryCombPromotionParam: unsafe extern "C" fn(
        this: *mut CThostFtdcTraderApi,
        pQryCombPromotionParam: *mut CThostFtdcQryCombPromotionParamField,
        nRequestID: ::std::os::raw::c_int,
    )
        -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryRiskSettleInvstPosition:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryRiskSettleInvstPosition: *mut CThostFtdcQryRiskSettleInvstPositionField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
    pub CThostFtdcTraderApi_ReqQryRiskSettleProductStatus:
        unsafe extern "C" fn(
            this: *mut CThostFtdcTraderApi,
            pQryRiskSettleProductStatus: *mut CThostFtdcQryRiskSettleProductStatusField,
            nRequestID: ::std::os::raw::c_int,
        ) -> ::std::os::raw::c_int,
}
#[repr(C)]
#[derive(Debug)]
pub struct CThostFtdcTraderApi {
    pub vtable_: *const CThostFtdcTraderApi__bindgen_vtable,
}
extern "C" {
    #[link_name = "\u{1}?CreateFtdcTraderApi@CThostFtdcTraderApi@@SAPEAV1@PEBD@Z"]
    pub fn CThostFtdcTraderApi_CreateFtdcTraderApi(
        pszFlowPath: *const ::std::os::raw::c_char,
    ) -> *mut CThostFtdcTraderApi;
}
extern "C" {
    #[link_name = "\u{1}?GetApiVersion@CThostFtdcTraderApi@@SAPEBDXZ"]
    pub fn CThostFtdcTraderApi_GetApiVersion() -> *const ::std::os::raw::c_char;
}
impl Default for CThostFtdcTraderApi {
    fn default() -> Self {
        let mut s = ::std::mem::MaybeUninit::<Self>::uninit();
        unsafe {
            ::std::ptr::write_bytes(s.as_mut_ptr(), 0, 1);
            s.assume_init()
        }
    }
}
impl CThostFtdcTraderApi {
    #[inline]
    pub unsafe fn CreateFtdcTraderApi(
        pszFlowPath: *const ::std::os::raw::c_char,
    ) -> *mut CThostFtdcTraderApi {
        CThostFtdcTraderApi_CreateFtdcTraderApi(pszFlowPath)
    }
    #[inline]
    pub unsafe fn GetApiVersion() -> *const ::std::os::raw::c_char {
        CThostFtdcTraderApi_GetApiVersion()
    }
}
