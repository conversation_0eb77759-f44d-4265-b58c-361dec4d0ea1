impl :: bincode :: Encode for CThostFtdcLogoutAllField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.FrontID, encoder) ?; :: bincode
        :: Encode :: encode(&self.SessionID, encoder) ?; :: bincode :: Encode
        :: encode(&self.SystemName, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}