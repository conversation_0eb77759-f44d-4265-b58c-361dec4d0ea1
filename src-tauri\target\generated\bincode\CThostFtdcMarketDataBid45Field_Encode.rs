impl :: bincode :: Encode for CThostFtdcMarketDataBid45Field
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BidPrice4, encoder) ?; :: bincode
        :: Encode :: encode(&self.BidVolume4, encoder) ?; :: bincode :: Encode
        :: encode(&self.BidPrice5, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume5, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}