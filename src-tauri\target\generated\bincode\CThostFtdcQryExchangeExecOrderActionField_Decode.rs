impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcQryExchangeExecOrderActionField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ParticipantID : :: bincode :: Decode :: decode(decoder) ?,
            ClientID : :: bincode :: Decode :: decode(decoder) ?, ExchangeID :
            :: bincode :: Decode :: decode(decoder) ?, TraderID : :: bincode
            :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcQryExchangeExecOrderActionField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ParticipantID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClientID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TraderID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}