impl :: bincode :: Encode for CThostFtdcParkedOrderActionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.OrderActionRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrontID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OrderSysID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.LimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeChange, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.ParkedOrderActionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserType, encoder) ?; :: bincode :: Encode ::
        encode(&self.Status, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ErrorMsg, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}