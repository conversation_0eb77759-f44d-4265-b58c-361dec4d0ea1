impl :: bincode :: Encode for CThostFtdcExchangeMarginRateAdjustField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode
        :: encode(&self.He<PERSON><PERSON>lag, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongMarginRatioByMoney, encoder) ?; :: bincode :: Encode
        :: encode(&self.LongMarginRatioByVolume, encoder) ?; :: bincode ::
        Encode :: encode(&self.ShortMarginRatioByMoney, encoder) ?; :: bincode
        :: Encode :: encode(&self.ShortMarginRatioByVolume, encoder) ?; ::
        bincode :: Encode :: encode(&self.ExchLongMarginRatioByMoney, encoder)
        ?; :: bincode :: Encode ::
        encode(&self.ExchLongMarginRatioByVolume, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchShortMarginRatioByMoney, encoder) ?; ::
        bincode :: Encode ::
        encode(&self.ExchShortMarginRatioByVolume, encoder) ?; :: bincode ::
        Encode :: encode(&self.NoLongMarginRatioByMoney, encoder) ?; ::
        bincode :: Encode :: encode(&self.NoLongMarginRatioByVolume, encoder)
        ?; :: bincode :: Encode ::
        encode(&self.NoShortMarginRatioByMoney, encoder) ?; :: bincode ::
        Encode :: encode(&self.NoShortMarginRatioByVolume, encoder) ?; ::
        bincode :: Encode :: encode(&self.InstrumentID, encoder) ?; core ::
        result :: Result :: Ok(())
    }
}