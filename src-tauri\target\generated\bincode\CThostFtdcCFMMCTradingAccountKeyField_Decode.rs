impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcCFMMCTradingAccount<PERSON>ey<PERSON>ield
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?,
            ParticipantID : :: bincode :: Decode :: decode(decoder) ?,
            AccountID : :: bincode :: Decode :: decode(decoder) ?, KeyID : ::
            bincode :: Decode :: decode(decoder) ?, CurrentKey : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcCFMMCTradingAccountKeyField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ParticipantID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AccountID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, KeyID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, CurrentKey : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}