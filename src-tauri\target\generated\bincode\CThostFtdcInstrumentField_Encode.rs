impl :: bincode :: Encode for CThostFtdcInstrumentField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode
        :: encode(&self.InstrumentName, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve3, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProductClass, encoder) ?; :: bincode :: Encode ::
        encode(&self.DeliveryYear, encoder) ?; :: bincode :: Encode ::
        encode(&self.DeliveryMonth, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxMarketOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MinMarketOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxLimitOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MinLimitOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.PriceTick, encoder) ?; :: bincode :: Encode ::
        encode(&self.CreateDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExpireDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.StartDelivDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.EndDelivDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstLifePhase, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsTrading, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionType, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionDateType, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongMarginRatio, encoder) ?; :: bincode :: Encode ::
        encode(&self.ShortMarginRatio, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxMarginSideAlgorithm, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve4, encoder) ?; :: bincode :: Encode ::
        encode(&self.StrikePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.OptionsType, encoder) ?; :: bincode :: Encode ::
        encode(&self.UnderlyingMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombinationType, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProductID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UnderlyingInstrID, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}