impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcDRTransferField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            OrigDRIdentityID : :: bincode :: Decode :: decode(decoder) ?,
            DestDRIdentityID : :: bincode :: Decode :: decode(decoder) ?,
            OrigBrokerID : :: bincode :: Decode :: decode(decoder) ?,
            DestBrokerID : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcDRTransferField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            OrigDRIdentityID : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, DestDRIdentityID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OrigBrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, DestBrokerID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}