impl :: bincode :: Encode for CThostFtdcCFMMCTradingAccountKeyField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ParticipantID, encoder) ?; :: bincode ::
        Encode :: encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.KeyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Current<PERSON><PERSON>, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}