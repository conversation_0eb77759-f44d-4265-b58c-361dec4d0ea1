impl :: bincode :: Encode for CThostFtdcQrySyncFundMortgageField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.MortgageSeqNo, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}