impl :: bincode :: Encode for CThostFtdcInvestorWithdrawAlgorithmField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorRange, encoder) ?; :: bincode ::
        Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UsingRatio, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FundMortgageRatio, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}