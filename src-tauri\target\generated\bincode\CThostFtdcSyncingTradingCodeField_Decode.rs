impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncingTradingCodeField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InvestorID : :: bincode :: Decode :: decode(decoder) ?, BrokerID :
            :: bincode :: Decode :: decode(decoder) ?, ExchangeID : :: bincode
            :: Decode :: decode(decoder) ?, ClientID : :: bincode :: Decode ::
            decode(decoder) ?, IsActive : :: bincode :: Decode ::
            decode(decoder) ?, ClientIDType : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncingTrading<PERSON>odeField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InvestorID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClientID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, IsActive : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClientIDType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}