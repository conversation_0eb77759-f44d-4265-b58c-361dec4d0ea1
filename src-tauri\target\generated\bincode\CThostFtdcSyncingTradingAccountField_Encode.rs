impl :: bincode :: Encode for CThostFtdcSyncingTradingAccountField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.AccountID, encoder) ?; :: bincode :: Encode
        :: encode(&self.PreMortgage, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreCredit, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreDeposit, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreBalance, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.InterestBase, encoder) ?; :: bincode :: Encode ::
        encode(&self.Interest, encoder) ?; :: bincode :: Encode ::
        encode(&self.Deposit, encoder) ?; :: bincode :: Encode ::
        encode(&self.Withdraw, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenCash, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenCommission, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.CashIn, encoder) ?; :: bincode :: Encode ::
        encode(&self.Commission, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfit, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionProfit, encoder) ?; :: bincode :: Encode ::
        encode(&self.Balance, encoder) ?; :: bincode :: Encode ::
        encode(&self.Available, encoder) ?; :: bincode :: Encode ::
        encode(&self.WithdrawQuota, encoder) ?; :: bincode :: Encode ::
        encode(&self.Reserve, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Credit, encoder) ?; :: bincode :: Encode ::
        encode(&self.Mortgage, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.DeliveryMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeDeliveryMargin, encoder) ?; :: bincode :: Encode
        :: encode(&self.ReserveBalance, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreFundMortgageIn, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreFundMortgageOut, encoder) ?; :: bincode :: Encode ::
        encode(&self.FundMortgageIn, encoder) ?; :: bincode :: Encode ::
        encode(&self.FundMortgageOut, encoder) ?; :: bincode :: Encode ::
        encode(&self.FundMortgageAvailable, encoder) ?; :: bincode :: Encode
        :: encode(&self.MortgageableFund, encoder) ?; :: bincode :: Encode ::
        encode(&self.SpecProductMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.SpecProductFrozenMargin, encoder) ?; :: bincode :: Encode
        :: encode(&self.SpecProductCommission, encoder) ?; :: bincode ::
        Encode :: encode(&self.SpecProductFrozenCommission, encoder) ?; ::
        bincode :: Encode :: encode(&self.SpecProductPositionProfit, encoder)
        ?; :: bincode :: Encode ::
        encode(&self.SpecProductCloseProfit, encoder) ?; :: bincode :: Encode
        :: encode(&self.SpecProductPositionProfitByAlg, encoder) ?; :: bincode
        :: Encode :: encode(&self.SpecProductExchangeMargin, encoder) ?; ::
        bincode :: Encode :: encode(&self.FrozenSwap, encoder) ?; :: bincode
        :: Encode :: encode(&self.RemainSwap, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}