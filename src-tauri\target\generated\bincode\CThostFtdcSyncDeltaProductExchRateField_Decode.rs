impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDeltaProductExchRateField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ProductID : :: bincode :: Decode :: decode(decoder) ?,
            QuoteCurrencyID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeRate : :: bincode :: Decode :: decode(decoder) ?,
            ActionDirection : :: bincode :: Decode :: decode(decoder) ?,
            SyncDeltaSequenceNo : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDeltaProductExchRateField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ProductID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, QuoteCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeRate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ActionDirection : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?,
        })
    }
}