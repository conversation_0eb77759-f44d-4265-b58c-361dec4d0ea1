impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDeltaOptInvstCommRateField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InstrumentID : :: bincode :: Decode :: decode(decoder) ?,
            InvestorRange : :: bincode :: Decode :: decode(decoder) ?,
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, OpenRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?, OpenRatioByVolume : ::
            bincode :: Decode :: decode(decoder) ?, CloseRatioByMoney : ::
            bincode :: Decode :: decode(decoder) ?, CloseRatioByVolume : ::
            bincode :: Decode :: decode(decoder) ?, CloseTodayRatioByMoney :
            :: bincode :: Decode :: decode(decoder) ?, CloseTodayRatioByVolume
            : :: bincode :: Decode :: decode(decoder) ?, StrikeRatioByMoney :
            :: bincode :: Decode :: decode(decoder) ?, StrikeRatioByVolume :
            :: bincode :: Decode :: decode(decoder) ?, ActionDirection : ::
            bincode :: Decode :: decode(decoder) ?, SyncDeltaSequenceNo : ::
            bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDeltaOptInvstCommRateField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            InstrumentID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorRange : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OpenRatioByMoney :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OpenRatioByVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CloseRatioByMoney : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, CloseRatioByVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CloseTodayRatioByMoney : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, CloseTodayRatioByVolume :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, StrikeRatioByMoney : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            StrikeRatioByVolume : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ActionDirection : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?,
        })
    }
}