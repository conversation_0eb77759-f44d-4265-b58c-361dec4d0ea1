impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcExchangeTradeField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: Decode :: decode(decoder) ?, TradeID :
            :: bincode :: Decode :: decode(decoder) ?, Direction : :: bincode
            :: Decode :: decode(decoder) ?, OrderSysID : :: bincode :: Decode
            :: decode(decoder) ?, ParticipantID : :: bincode :: Decode ::
            decode(decoder) ?, ClientID : :: bincode :: Decode ::
            decode(decoder) ?, TradingRole : :: bincode :: Decode ::
            decode(decoder) ?, reserve1 : :: bincode :: Decode ::
            decode(decoder) ?, OffsetFlag : :: bincode :: Decode ::
            decode(decoder) ?, HedgeFlag : :: bincode :: Decode ::
            decode(decoder) ?, Price : :: bincode :: Decode :: decode(decoder)
            ?, Volume : :: bincode :: Decode :: decode(decoder) ?, TradeDate :
            :: bincode :: Decode :: decode(decoder) ?, TradeTime : :: bincode
            :: Decode :: decode(decoder) ?, TradeType : :: bincode :: Decode
            :: decode(decoder) ?, PriceSource : :: bincode :: Decode ::
            decode(decoder) ?, TraderID : :: bincode :: Decode ::
            decode(decoder) ?, OrderLocalID : :: bincode :: Decode ::
            decode(decoder) ?, ClearingPartID : :: bincode :: Decode ::
            decode(decoder) ?, BusinessUnit : :: bincode :: Decode ::
            decode(decoder) ?, SequenceNo : :: bincode :: Decode ::
            decode(decoder) ?, TradeSource : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeInstID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcExchangeTradeField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, Direction : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OrderSysID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ParticipantID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ClientID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradingRole : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, reserve1 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OffsetFlag : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, HedgeFlag : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Price : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, Volume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TradeDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeType : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PriceSource : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TraderID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderLocalID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ClearingPartID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BusinessUnit : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SequenceNo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeSource : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeInstID :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}