impl :: bincode :: Encode for CThostFtdcUserPasswordUpdateField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OldPassword, encoder) ?; :: bincode :: Encode ::
        encode(&self.NewPassword, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}