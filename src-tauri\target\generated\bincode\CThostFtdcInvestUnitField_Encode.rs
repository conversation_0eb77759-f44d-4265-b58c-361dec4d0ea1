impl :: bincode :: Encode for CThostFtdcInvestUnitField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestorUnitName, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestorGroupID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CommModelID, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginModelID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}