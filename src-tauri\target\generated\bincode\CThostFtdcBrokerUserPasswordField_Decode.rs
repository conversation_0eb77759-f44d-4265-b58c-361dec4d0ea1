impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBrokerUserPasswordField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, UserID : ::
            bincode :: Decode :: decode(decoder) ?, Password : :: bincode ::
            Decode :: decode(decoder) ?, LastUpdateTime : :: bincode :: Decode
            :: decode(decoder) ?, LastLoginTime : :: bincode :: Decode ::
            decode(decoder) ?, ExpireDate : :: bincode :: Decode ::
            decode(decoder) ?, WeakExpireDate : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBrokerUserPasswordField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, Password : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LastUpdateTime : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, LastLoginTime : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExpireDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, WeakExpireDate : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}