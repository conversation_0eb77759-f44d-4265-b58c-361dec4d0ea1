impl :: bincode :: Encode for CThostFtdcDisseminationField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.SequenceSeries, encoder) ?; ::
        bincode :: Encode :: encode(&self.SequenceNo, encoder) ?; core ::
        result :: Result :: Ok(())
    }
}