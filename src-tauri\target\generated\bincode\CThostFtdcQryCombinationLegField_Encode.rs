impl :: bincode :: Encode for CThostFtdcQryCombinationLegField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.LegID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombInstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.LegInstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}