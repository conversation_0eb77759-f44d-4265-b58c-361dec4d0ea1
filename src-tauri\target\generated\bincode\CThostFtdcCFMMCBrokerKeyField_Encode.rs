impl :: bincode :: Encode for CThostFtdcCFMMCBrokerKeyField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ParticipantID, encoder) ?; :: bincode ::
        Encode :: encode(&self.CreateDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.CreateTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.KeyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Current<PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON>, encoder) ?; core :: result :: Result :: Ok(())
    }
}