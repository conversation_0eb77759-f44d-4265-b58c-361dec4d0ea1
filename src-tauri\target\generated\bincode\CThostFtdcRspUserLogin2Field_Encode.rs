impl :: bincode :: Encode for CThostFtdcRspUserLogin2Field
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.LoginTime, encoder) ?; :: bincode ::
        Encode :: encode(&self.BrokerID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SystemName, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrontID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxOrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.SHFETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.DCETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.CZCETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.FFEXTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.INETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.RandomString, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}