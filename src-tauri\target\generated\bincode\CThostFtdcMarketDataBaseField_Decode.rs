impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcMarketDataBaseField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: Decode :: decode(decoder) ?,
            PreSettlementPrice : :: bincode :: Decode :: decode(decoder) ?,
            PreClosePrice : :: bincode :: Decode :: decode(decoder) ?,
            PreOpenInterest : :: bincode :: Decode :: decode(decoder) ?,
            PreDelta : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcMarketDataBaseField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PreSettlementPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PreClosePrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PreOpenInterest : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PreDelta : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}