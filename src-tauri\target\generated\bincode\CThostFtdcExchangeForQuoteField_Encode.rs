impl :: bincode :: Encode for CThostFtdcExchangeForQuoteField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ForQuoteLocalID, encoder) ?; ::
        bincode :: Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ParticipantID, encoder) ?; :: bincode ::
        Encode :: encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.TraderID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstallID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.InsertTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForQuoteStatus, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeInstID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}