impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcCFMMCBrokerKeyField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?,
            ParticipantID : :: bincode :: Decode :: decode(decoder) ?,
            CreateDate : :: bincode :: Decode :: decode(decoder) ?, CreateTime
            : :: bincode :: Decode :: decode(decoder) ?, KeyID : :: bincode ::
            Decode :: decode(decoder) ?, CurrentKey : :: bincode :: Decode ::
            decode(decoder) ?, KeyKind : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcCFMMCBroker<PERSON>eyField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ParticipantID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CreateDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CreateTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, KeyID : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CurrentKey : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, KeyKind : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}