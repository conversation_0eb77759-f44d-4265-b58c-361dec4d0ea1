impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcManualSyncBrokerUserOTPField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, UserID : ::
            bincode :: Decode :: decode(decoder) ?, OTPType : :: bincode ::
            Decode :: decode(decoder) ?, FirstOTP : :: bincode :: Decode ::
            decode(decoder) ?, SecondOTP : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcManualSyncBrokerUserOT<PERSON><PERSON>ield
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UserID : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, OTPType : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            FirstOTP : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SecondOTP : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}