{"rustc": 1842507548689473721, "features": "", "declared_features": "", "target": 0, "profile": 0, "path": 0, "deps": [[14039947826026167952, "build_script_build", false, 12253180331563528310], [16702348383442838006, "build_script_build", false, 970447816918275947], [15180317924524316486, "build_script_build", false, 6602679441824042945]], "local": [{"RerunIfChanged": {"output": "debug\\build\\tauri_app_vue-2f1d9df0d08ec74b\\output", "paths": ["wrapper.hpp", "tauri.conf.json", "capabilities"]}}, {"RerunIfEnvChanged": {"var": "TAURI_CONFIG", "val": null}}, {"RerunIfEnvChanged": {"var": "REMOVE_UNUSED_COMMANDS", "val": null}}], "rustflags": [], "config": 0, "compile_kind": 0}