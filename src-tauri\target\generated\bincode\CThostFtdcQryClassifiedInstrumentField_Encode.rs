impl :: bincode :: Encode for CThostFtdcQryClassifiedInstrumentField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.InstrumentID, encoder) ?; ::
        bincode :: Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ExchangeInstID, encoder) ?; :: bincode ::
        Encode :: encode(&self.ProductID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingType, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClassType, encoder) ?; core :: result :: Result :: Ok(())
    }
}