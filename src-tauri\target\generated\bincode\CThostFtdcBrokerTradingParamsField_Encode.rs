impl :: bincode :: Encode for CThostFtdcBrokerTradingParamsField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.MarginPriceType, encoder) ?; :: bincode :: Encode ::
        encode(&self.Algorithm, encoder) ?; :: bincode :: Encode ::
        encode(&self.AvailIncludeCloseProfit, encoder) ?; :: bincode :: Encode
        :: encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OptionRoyaltyPriceType, encoder) ?; :: bincode :: Encode
        :: encode(&self.Account<PERSON>, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}