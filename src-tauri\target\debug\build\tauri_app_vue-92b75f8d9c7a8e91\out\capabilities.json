{"contract-search": {"identifier": "contract-search", "description": "Capability for contract search windows", "local": true, "windows": ["contract-search-*"], "permissions": ["core:default", "core:app:default", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unminimize", "core:window:allow-set-focus", "core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-set-resizable", "core:window:allow-set-decorations", "core:window:allow-set-always-on-top", "core:window:allow-is-visible", "core:window:allow-is-minimized", "core:window:allow-is-maximized", "core:window:allow-outer-position", "core:window:allow-outer-size", "core:window:allow-destroy", "core:event:allow-listen", "core:event:allow-unlisten", "core:event:allow-emit", "core:event:allow-emit-to"]}, "default": {"identifier": "default", "description": "Capability for the main window", "local": true, "windows": ["main"], "permissions": ["core:default", "opener:default", "core:app:default", "core:webview:allow-create-webview-window", "core:window:allow-set-resizable", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-set-fullscreen", "core:window:allow-set-always-on-top", "core:window:allow-set-decorations", "core:window:allow-set-min-size", "core:window:allow-set-max-size", "core:window:allow-set-title", "core:window:allow-set-icon", "core:window:allow-set-focus", "core:window:allow-unminimize", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-is-visible", "core:window:allow-is-minimized", "core:window:allow-is-maximized", "core:window:allow-outer-position", "core:window:allow-outer-size", "core:window:allow-start-dragging", "core:window:allow-start-resize-dragging", "core:event:allow-listen", "core:event:allow-unlisten", "core:menu:allow-create-default", "core:menu:allow-set-as-app-menu", "core:menu:allow-new", "core:menu:allow-popup", "core:menu:allow-append", "core:menu:allow-prepend", "core:menu:allow-insert", "core:menu:allow-remove", "core:menu:allow-remove-at", "core:menu:allow-items", "core:menu:allow-get", "core:menu:allow-text", "core:menu:allow-set-text", "core:menu:allow-is-enabled", "core:menu:allow-set-enabled", "core:menu:allow-set-accelerator", "core:window:allow-destroy"]}, "trading-panels": {"identifier": "trading-panels", "description": "Capability for trading panel windows", "local": true, "windows": ["trading-panel-*"], "permissions": ["core:default", "core:app:default", "core:window:allow-close", "core:window:allow-hide", "core:window:allow-show", "core:window:allow-minimize", "core:window:allow-maximize", "core:window:allow-unminimize", "core:window:allow-set-focus", "core:window:allow-set-title", "core:window:allow-set-size", "core:window:allow-set-position", "core:window:allow-set-resizable", "core:window:allow-set-decorations", "core:window:allow-set-always-on-top", "core:window:allow-is-visible", "core:window:allow-is-minimized", "core:window:allow-is-maximized", "core:window:allow-outer-position", "core:window:allow-outer-size", "core:window:allow-destroy", "core:event:allow-listen", "core:event:allow-unlisten"]}}