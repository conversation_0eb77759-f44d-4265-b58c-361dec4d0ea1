impl :: bincode :: Encode for CThostFtdcSyncDeltaInvstMarginRateField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.InstrumentID, encoder) ?; ::
        bincode :: Encode :: encode(&self.InvestorRange, encoder) ?; ::
        bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode ::
        Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.HedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongMarginRatioByMoney, encoder) ?; :: bincode :: Encode
        :: encode(&self.LongMarginRatioByVolume, encoder) ?; :: bincode ::
        Encode :: encode(&self.ShortMarginRatioByMoney, encoder) ?; :: bincode
        :: Encode :: encode(&self.ShortMarginRatioByVolume, encoder) ?; ::
        bincode :: Encode :: encode(&self.IsRelative, encoder) ?; :: bincode
        :: Encode :: encode(&self.ActionDirection, encoder) ?; :: bincode ::
        Encode :: encode(&self.SyncDeltaSequenceNo, encoder) ?; core :: result
        :: Result :: Ok(())
    }
}