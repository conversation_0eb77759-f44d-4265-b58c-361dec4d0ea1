impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcMarketDataBid23Field
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BidPrice2 : :: bincode :: Decode :: decode(decoder) ?, BidVolume2
            : :: bincode :: Decode :: decode(decoder) ?, BidPrice3 : ::
            bincode :: Decode :: decode(decoder) ?, BidVolume3 : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcMarketDataBid23Field
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BidPrice2 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidVolume2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidPrice3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidVolume3 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}