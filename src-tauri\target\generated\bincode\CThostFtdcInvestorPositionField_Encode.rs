impl :: bincode :: Encode for CThostFtdcInvestorPositionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode :: Encode
        :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.PosiDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.HedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.YdPosition, encoder) ?; :: bincode :: Encode ::
        encode(&self.Position, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongF<PERSON>zen, encoder) ?; :: bincode :: Encode ::
        encode(&self.ShortF<PERSON>zen, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongFrozenAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.ShortFrozenAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionCost, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.UseMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenCash, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrozenCommission, encoder) ?; :: bincode :: Encode ::
        encode(&self.CashIn, encoder) ?; :: bincode :: Encode ::
        encode(&self.Commission, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfit, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionProfit, encoder) ?; :: bincode :: Encode ::
        encode(&self.PreSettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenCost, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombPosition, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombLongFrozen, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombShortFrozen, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfitByDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfitByTrade, encoder) ?; :: bincode :: Encode ::
        encode(&self.TodayPosition, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByMoney, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.StrikeFrozen, encoder) ?; :: bincode :: Encode ::
        encode(&self.StrikeFrozenAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.AbandonFrozen, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.YdStrikeFrozen, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionCostOffset, encoder) ?; :: bincode :: Encode ::
        encode(&self.TasPosition, encoder) ?; :: bincode :: Encode ::
        encode(&self.TasPositionCost, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}