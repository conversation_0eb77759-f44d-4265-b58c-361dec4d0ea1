impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcIPListField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?, IsWhite : ::
            bincode :: Decode :: decode(decoder) ?, IPAddress : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcIPListField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IsWhite : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, IPAddress : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}