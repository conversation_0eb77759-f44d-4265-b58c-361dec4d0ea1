impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDelaySwapField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            DelaySwapSeqNo : :: bincode :: Decode :: decode(decoder) ?,
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, FromCurrencyID : ::
            bincode :: Decode :: decode(decoder) ?, FromAmount : :: bincode ::
            Decode :: decode(decoder) ?, FromFrozenSwap : :: bincode :: Decode
            :: decode(decoder) ?, FromRemainSwap : :: bincode :: Decode ::
            decode(decoder) ?, ToCurrencyID : :: bincode :: Decode ::
            decode(decoder) ?, ToAmount : :: bincode :: Decode ::
            decode(decoder) ?, IsManualSwap : :: bincode :: Decode ::
            decode(decoder) ?, IsAllRemainSetZero : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDelaySwapField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            DelaySwapSeqNo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FromCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            FromAmount : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FromFrozenSwap : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            FromRemainSwap : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ToCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ToAmount : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IsManualSwap : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IsAllRemainSetZero : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?,
        })
    }
}