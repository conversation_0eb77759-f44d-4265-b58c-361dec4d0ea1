impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcMarketDataAsk23Field
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            AskPrice2 : :: bincode :: Decode :: decode(decoder) ?, AskVolume2
            : :: bincode :: Decode :: decode(decoder) ?, AskPrice3 : ::
            bincode :: Decode :: decode(decoder) ?, AskVolume3 : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcMarketDataAsk23Field
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            AskPrice2 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskVolume2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskPrice3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskVolume3 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}