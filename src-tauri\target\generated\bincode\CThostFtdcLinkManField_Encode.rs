impl :: bincode :: Encode for CThostFtdcLinkManField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.PersonType, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdentifiedCardType, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdentifiedCardNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.PersonName, encoder) ?; :: bincode :: Encode ::
        encode(&self.Telephone, encoder) ?; :: bincode :: Encode ::
        encode(&self.Address, encoder) ?; :: bincode :: Encode ::
        encode(&self.ZipCode, encoder) ?; :: bincode :: Encode ::
        encode(&self.Priority, encoder) ?; :: bincode :: Encode ::
        encode(&self.UOAZipCode, encoder) ?; :: bincode :: Encode ::
        encode(&self.PersonFullName, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}