impl :: bincode :: Encode for CThostFtdcSyncDeltaEWarrantOffsetField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradingDay, encoder) ?; ::
        bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode ::
        Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Direction, encoder) ?; :: bincode :: Encode ::
        encode(&self.He<PERSON><PERSON>lag, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.SyncDeltaSequenceNo, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}