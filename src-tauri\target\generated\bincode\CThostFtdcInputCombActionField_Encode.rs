impl :: bincode :: Encode for CThostFtdcInputCombActionField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombActionRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Direction, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FrontID, encoder) ?; :: bincode :: Encode ::
        encode(&self.SessionID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}