impl :: bincode :: Encode for CThostFtdcMarketDataUpdateTimeField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.UpdateTime, encoder) ?; :: bincode :: Encode
        :: encode(&self.UpdateMillisec, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}