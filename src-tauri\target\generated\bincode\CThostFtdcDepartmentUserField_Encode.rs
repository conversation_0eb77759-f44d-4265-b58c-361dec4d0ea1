impl :: bincode :: Encode for CThostFtdcDepartmentUserField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestorRange, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestorID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}