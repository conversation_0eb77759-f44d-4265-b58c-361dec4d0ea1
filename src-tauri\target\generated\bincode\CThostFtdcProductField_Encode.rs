impl :: bincode :: Encode for CThostFtdcProductField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.ProductName, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProductClass, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.PriceTick, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxMarketOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MinMarketOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxLimitOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.MinLimitOrderVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionType, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionDateType, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseDealType, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeCurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.MortgageFundUseRange, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.UnderlyingMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProductID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeProductID, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}