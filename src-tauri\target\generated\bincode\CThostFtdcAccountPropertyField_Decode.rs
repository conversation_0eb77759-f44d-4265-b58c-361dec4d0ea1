impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcAccountPropertyField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, AccountID :
            :: bincode :: Decode :: decode(decoder) ?, BankID : :: bincode ::
            Decode :: decode(decoder) ?, BankAccount : :: bincode :: Decode ::
            decode(decoder) ?, OpenName : :: bincode :: Decode ::
            decode(decoder) ?, OpenBank : :: bincode :: Decode ::
            decode(decoder) ?, IsActive : :: bincode :: Decode ::
            decode(decoder) ?, AccountSourceType : :: bincode :: Decode ::
            decode(decoder) ?, OpenDate : :: bincode :: Decode ::
            decode(decoder) ?, CancelDate : :: bincode :: Decode ::
            decode(decoder) ?, OperatorID : :: bincode :: Decode ::
            decode(decoder) ?, OperateDate : :: bincode :: Decode ::
            decode(decoder) ?, OperateTime : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcAccountPropertyField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AccountID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BankID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BankAccount : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OpenName : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OpenBank : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, IsActive : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AccountSourceType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OpenDate : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CancelDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OperatorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OperateDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OperateTime : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CurrencyID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}