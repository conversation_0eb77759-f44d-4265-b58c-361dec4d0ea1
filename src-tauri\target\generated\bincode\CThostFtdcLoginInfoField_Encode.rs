impl :: bincode :: Encode for CThostFtdcLoginInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.FrontID, encoder) ?; :: bincode
        :: Encode :: encode(&self.SessionID, encoder) ?; :: bincode :: Encode
        :: encode(&self.BrokerID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.LoginDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.LoginTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserProductInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.InterfaceProductInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProtocolInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.SystemName, encoder) ?; :: bincode :: Encode ::
        encode(&self.PasswordDeprecated, encoder) ?; :: bincode :: Encode ::
        encode(&self.MaxOrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.SHFETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.DCETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.CZCETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.FFEXTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.OneTimePassword, encoder) ?; :: bincode :: Encode ::
        encode(&self.INETime, encoder) ?; :: bincode :: Encode ::
        encode(&self.IsQryControl, encoder) ?; :: bincode :: Encode ::
        encode(&self.LoginRemark, encoder) ?; :: bincode :: Encode ::
        encode(&self.Password, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}