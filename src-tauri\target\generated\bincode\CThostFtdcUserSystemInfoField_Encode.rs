impl :: bincode :: Encode for CThostFtdcUserSystemInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientSystemInfoLen, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientSystemInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientIPPort, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientLoginTime, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientAppID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientPublicIP, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientLoginRemark, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}