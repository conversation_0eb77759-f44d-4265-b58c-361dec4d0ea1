impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcExchangeOrderActionErrorField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: Decode :: decode(decoder) ?, OrderSysID
            : :: bincode :: Decode :: decode(decoder) ?, TraderID : :: bincode
            :: Decode :: decode(decoder) ?, InstallID : :: bincode :: Decode
            :: decode(decoder) ?, OrderLocalID : :: bincode :: Decode ::
            decode(decoder) ?, ActionLocalID : :: bincode :: Decode ::
            decode(decoder) ?, ErrorID : :: bincode :: Decode ::
            decode(decoder) ?, ErrorMsg : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcExchangeOrderActionErrorField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OrderSysID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TraderID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstallID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OrderLocalID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ActionLocalID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ErrorID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ErrorMsg : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}