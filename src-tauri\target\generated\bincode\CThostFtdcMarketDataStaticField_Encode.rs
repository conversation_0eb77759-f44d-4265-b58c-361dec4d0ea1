impl :: bincode :: Encode for CThostFtdcMarketDataStaticField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.OpenPrice, encoder) ?; :: bincode
        :: Encode :: encode(&self.HighestPrice, encoder) ?; :: bincode ::
        Encode :: encode(&self.LowestPrice, encoder) ?; :: bincode :: Encode
        :: encode(&self.ClosePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.UpperLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.LowerLimitPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, encoder) ?; core :: result :: Result :: Ok(())
    }
}