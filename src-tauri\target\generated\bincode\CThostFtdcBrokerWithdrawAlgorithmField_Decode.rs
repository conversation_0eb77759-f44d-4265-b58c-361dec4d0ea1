impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcBrokerWithdrawAlgorithmField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?,
            WithdrawAlgorithm : :: bincode :: Decode :: decode(decoder) ?,
            UsingRatio : :: bincode :: Decode :: decode(decoder) ?,
            IncludeCloseProfit : :: bincode :: Decode :: decode(decoder) ?,
            AllWithoutTrade : :: bincode :: Decode :: decode(decoder) ?,
            AvailIncludeCloseProfit : :: bincode :: Decode :: decode(decoder)
            ?, IsBrokerUserEvent : :: bincode :: Decode :: decode(decoder) ?,
            CurrencyID : :: bincode :: Decode :: decode(decoder) ?,
            FundMortgageRatio : :: bincode :: Decode :: decode(decoder) ?,
            BalanceAlgorithm : :: bincode :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcBrokerWithdrawAlgorithmField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, WithdrawAlgorithm : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            UsingRatio : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IncludeCloseProfit : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            AllWithoutTrade : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AvailIncludeCloseProfit : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            IsBrokerUserEvent : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, CurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            FundMortgageRatio : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, BalanceAlgorithm : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}