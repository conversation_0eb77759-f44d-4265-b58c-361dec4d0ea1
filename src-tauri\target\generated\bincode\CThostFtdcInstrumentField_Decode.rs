impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInstrumentField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?, ExchangeID :
            :: bincode :: Decode :: decode(decoder) ?, InstrumentName : ::
            bincode :: Decode :: decode(decoder) ?, reserve2 : :: bincode ::
            Decode :: decode(decoder) ?, reserve3 : :: bincode :: Decode ::
            decode(decoder) ?, ProductClass : :: bincode :: Decode ::
            decode(decoder) ?, DeliveryYear : :: bincode :: Decode ::
            decode(decoder) ?, DeliveryMonth : :: bincode :: Decode ::
            decode(decoder) ?, MaxMarketOrderVolume : :: bincode :: Decode ::
            decode(decoder) ?, MinMarketOrderVolume : :: bincode :: Decode ::
            decode(decoder) ?, MaxLimitOrderVolume : :: bincode :: Decode ::
            decode(decoder) ?, MinLimitOrderVolume : :: bincode :: Decode ::
            decode(decoder) ?, VolumeMultiple : :: bincode :: Decode ::
            decode(decoder) ?, PriceTick : :: bincode :: Decode ::
            decode(decoder) ?, CreateDate : :: bincode :: Decode ::
            decode(decoder) ?, OpenDate : :: bincode :: Decode ::
            decode(decoder) ?, ExpireDate : :: bincode :: Decode ::
            decode(decoder) ?, StartDelivDate : :: bincode :: Decode ::
            decode(decoder) ?, EndDelivDate : :: bincode :: Decode ::
            decode(decoder) ?, InstLifePhase : :: bincode :: Decode ::
            decode(decoder) ?, IsTrading : :: bincode :: Decode ::
            decode(decoder) ?, PositionType : :: bincode :: Decode ::
            decode(decoder) ?, PositionDateType : :: bincode :: Decode ::
            decode(decoder) ?, LongMarginRatio : :: bincode :: Decode ::
            decode(decoder) ?, ShortMarginRatio : :: bincode :: Decode ::
            decode(decoder) ?, MaxMarginSideAlgorithm : :: bincode :: Decode
            :: decode(decoder) ?, reserve4 : :: bincode :: Decode ::
            decode(decoder) ?, StrikePrice : :: bincode :: Decode ::
            decode(decoder) ?, OptionsType : :: bincode :: Decode ::
            decode(decoder) ?, UnderlyingMultiple : :: bincode :: Decode ::
            decode(decoder) ?, CombinationType : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeInstID : :: bincode :: Decode ::
            decode(decoder) ?, ProductID : :: bincode :: Decode ::
            decode(decoder) ?, UnderlyingInstrID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInstrumentField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchangeID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InstrumentName :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, reserve3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ProductClass : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            DeliveryYear : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, DeliveryMonth : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MaxMarketOrderVolume : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, MinMarketOrderVolume : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, MaxLimitOrderVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MinLimitOrderVolume : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, VolumeMultiple : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PriceTick : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CreateDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, OpenDate : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExpireDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, StartDelivDate :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, EndDelivDate : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InstLifePhase : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, IsTrading : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, PositionType : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PositionDateType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LongMarginRatio : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ShortMarginRatio : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MaxMarginSideAlgorithm : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, reserve4 : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            StrikePrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OptionsType : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UnderlyingMultiple
            : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CombinationType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InstrumentID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchangeInstID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ProductID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UnderlyingInstrID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}