impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcTradeParamField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, TradeParamID
            : :: bincode :: Decode :: decode(decoder) ?, TradeParamValue : ::
            bincode :: Decode :: decode(decoder) ?, Memo : :: bincode ::
            Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcTradeParamField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TradeParamID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TradeParamValue : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Memo : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}