impl :: bincode :: Encode for CThostFtdcInputQuoteField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.QuoteRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BusinessUnit, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskOffsetFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidOffsetFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskHedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidHedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.AskOrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.BidOrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.ForQuoteSysID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.ReplaceSysID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}