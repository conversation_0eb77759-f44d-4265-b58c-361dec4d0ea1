impl :: bincode :: Encode for CThostFtdcRspAuthenticateField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserProductInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.AppID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AppType, encoder) ?; core :: result :: Result :: Ok(())
    }
}