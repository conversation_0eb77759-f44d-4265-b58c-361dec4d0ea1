impl :: bincode :: Encode for CThostFtdcMarginModelField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.MarginModelID, encoder) ?; :: bincode ::
        Encode :: encode(&self.MarginModelName, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}