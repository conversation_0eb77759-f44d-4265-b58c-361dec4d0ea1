impl :: bincode :: Encode for CThostFtdcQryTradeField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeTimeStart, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeTimeEnd, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}