impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSettlementInfoConfirmField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, ConfirmDate : ::
            bincode :: Decode :: decode(decoder) ?, ConfirmTime : :: bincode
            :: Decode :: decode(decoder) ?, SettlementID : :: bincode ::
            Decode :: decode(decoder) ?, AccountID : :: bincode :: Decode ::
            decode(decoder) ?, CurrencyID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSettlementInfoConfirmField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            BrokerID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InvestorID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ConfirmDate : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ConfirmTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, SettlementID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AccountID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, CurrencyID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?,
        })
    }
}