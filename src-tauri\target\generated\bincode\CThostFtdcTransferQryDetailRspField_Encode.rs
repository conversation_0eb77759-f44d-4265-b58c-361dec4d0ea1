impl :: bincode :: Encode for CThostFtdcTransferQryDetailRspField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradeDate, encoder) ?; :: bincode
        :: Encode :: encode(&self.TradeTime, encoder) ?; :: bincode :: Encode
        :: encode(&self.TradeCode, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureID, encoder) ?; :: bincode :: Encode ::
        encode(&self.FutureAccount, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankSerial, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankBrchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankAccount, encoder) ?; :: bincode :: Encode ::
        encode(&self.CertCode, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyCode, encoder) ?; :: bincode :: Encode ::
        encode(&self.TxAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.Flag, encoder) ?; core :: result :: Result :: Ok(())
    }
}