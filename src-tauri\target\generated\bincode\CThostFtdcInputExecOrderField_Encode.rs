impl :: bincode :: Encode for CThostFtdcInputExecOrderField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode
        :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExecOrderRef, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.RequestID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BusinessUnit, encoder) ?; :: bincode :: Encode ::
        encode(&self.OffsetFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.HedgeFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionType, encoder) ?; :: bincode :: Encode ::
        encode(&self.PosiDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.ReservePositionFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseFlag, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.InvestUnitID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ClientID, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve2, encoder) ?; :: bincode :: Encode ::
        encode(&self.MacAddress, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IPAddress, encoder) ?; core :: result :: Result :: Ok(())
    }
}