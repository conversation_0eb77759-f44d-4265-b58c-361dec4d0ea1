D:\demo\tauri-vue\src-tauri\target\debug\deps\tauri_app_vue-e1fe737f5eb6aea2.d: src\main.rs src\file_io.rs src\ctp_commands.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-2f1d9df0d08ec74b\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

D:\demo\tauri-vue\src-tauri\target\debug\deps\libtauri_app_vue-e1fe737f5eb6aea2.rmeta: src\main.rs src\file_io.rs src\ctp_commands.rs D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-2f1d9df0d08ec74b\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7

src\main.rs:
src\file_io.rs:
src\ctp_commands.rs:
D:\demo\tauri-vue\src-tauri\target\debug\build\tauri_app_vue-2f1d9df0d08ec74b\out/524e70828b177840a8abc536a997eb4579ce81975771e153329f0de43810a5a7:

# env-dep:CARGO_PKG_AUTHORS=you
# env-dep:CARGO_PKG_DESCRIPTION=A Tauri App
# env-dep:CARGO_PKG_NAME=tauri_app_vue
# env-dep:OUT_DIR=D:\\demo\\tauri-vue\\src-tauri\\target\\debug\\build\\tauri_app_vue-2f1d9df0d08ec74b\\out
