impl :: bincode :: Encode for CThostFtdcSyncDeltaInvstPosDtlField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.InstrumentID, encoder) ?; ::
        bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode ::
        Encode :: encode(&self.InvestorID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Hedge<PERSON>lag, encoder) ?; :: bincode :: Encode ::
        encode(&self.Direction, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.Volume, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradingDay, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementID, encoder) ?; :: bincode :: Encode ::
        encode(&self.TradeType, encoder) ?; :: bincode :: Encode ::
        encode(&self.CombInstrumentID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfitByDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseProfitByTrade, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionProfitByDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.PositionProfitByTrade, encoder) ?; :: bincode :: Encode
        :: encode(&self.Margin, encoder) ?; :: bincode :: Encode ::
        encode(&self.ExchMargin, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByMoney, encoder) ?; :: bincode :: Encode ::
        encode(&self.MarginRateByVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.LastSettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.SettlementPrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.CloseAmount, encoder) ?; :: bincode :: Encode ::
        encode(&self.TimeFirstVolume, encoder) ?; :: bincode :: Encode ::
        encode(&self.SpecPosiType, encoder) ?; :: bincode :: Encode ::
        encode(&self.ActionDirection, encoder) ?; :: bincode :: Encode ::
        encode(&self.SyncDeltaSequenceNo, encoder) ?; core :: result :: Result
        :: Ok(())
    }
}