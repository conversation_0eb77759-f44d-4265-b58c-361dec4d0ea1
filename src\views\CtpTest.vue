<template>
  <div class="ctp-test-container">
    <div class="test-panel">
      <h2>CTP连接测试</h2>
      
      <!-- 连接状态 -->
      <div class="status-section">
        <h3>连接状态</h3>
        <div class="status-item">
          <span>行情API状态:</span>
          <span :class="['status', mdStatus]">{{ mdStatusText }}</span>
        </div>
        <div class="status-item">
          <span>交易API状态:</span>
          <span :class="['status', traderStatus]">{{ traderStatusText }}</span>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-section">
        <h3>操作</h3>
        <div class="button-group">
          <button @click="testConnection" :disabled="testing">
            {{ testing ? '测试中...' : '测试连接' }}
          </button>
          <button @click="runQuickTest" :disabled="testing">
            {{ testing ? '测试中...' : '快速测试' }}
          </button>
          <button @click="subscribeMarketData" :disabled="!canSubscribe">
            订阅rb2509行情
          </button>
          <button @click="testInstruments" :disabled="testing">
            {{ testing ? '查询中...' : '测试合约查询' }}
          </button>
          <button @click="clearLogs">清除日志</button>
        </div>
      </div>

      <!-- 行情数据 -->
      <div class="market-data-section" v-if="marketData">
        <h3>行情数据 (rb2509)</h3>
        <div class="market-data">
          <div class="data-item">
            <span>最新价:</span>
            <span>{{ marketData.last_price }}</span>
          </div>
          <div class="data-item">
            <span>成交量:</span>
            <span>{{ marketData.volume }}</span>
          </div>
          <div class="data-item">
            <span>持仓量:</span>
            <span>{{ marketData.open_interest }}</span>
          </div>
          <div class="data-item">
            <span>买一价:</span>
            <span>{{ marketData.bid_price1 }}</span>
          </div>
          <div class="data-item">
            <span>买一量:</span>
            <span>{{ marketData.bid_volume1 }}</span>
          </div>
          <div class="data-item">
            <span>卖一价:</span>
            <span>{{ marketData.ask_price1 }}</span>
          </div>
          <div class="data-item">
            <span>卖一量:</span>
            <span>{{ marketData.ask_volume1 }}</span>
          </div>
        </div>
      </div>

      <!-- 合约数据 -->
      <div class="instruments-section" v-if="instruments.length > 0">
        <h3>合约数据 (共{{ instruments.length }}个)</h3>
        <div class="instruments-container">
          <div class="instruments-summary">
            <div class="summary-item">
              <span>上海期货交易所(SHFE):</span>
              <span>{{ instrumentsByExchange.SHFE || 0 }}个</span>
            </div>
            <div class="summary-item">
              <span>大连商品交易所(DCE):</span>
              <span>{{ instrumentsByExchange.DCE || 0 }}个</span>
            </div>
            <div class="summary-item">
              <span>郑州商品交易所(CZCE):</span>
              <span>{{ instrumentsByExchange.CZCE || 0 }}个</span>
            </div>
            <div class="summary-item">
              <span>中国金融期货交易所(CFFEX):</span>
              <span>{{ instrumentsByExchange.CFFEX || 0 }}个</span>
            </div>
          </div>
          <div class="instruments-list">
            <div
              v-for="instrument in instruments.slice(0, 10)"
              :key="instrument.instrument_id"
              class="instrument-item"
            >
              <span class="instrument-id">{{ instrument.instrument_id }}</span>
              <span class="instrument-name">{{ instrument.instrument_name }}</span>
              <span class="instrument-exchange">{{ instrument.exchange_id }}</span>
            </div>
            <div v-if="instruments.length > 10" class="more-instruments">
              ... 还有 {{ instruments.length - 10 }} 个合约
            </div>
          </div>
        </div>
      </div>

      <!-- 日志 -->
      <div class="log-section">
        <h3>日志</h3>
        <div class="log-container">
          <div 
            v-for="log in logs" 
            :key="log.timestamp"
            :class="['log-item', log.level]"
          >
            <span class="log-time">{{ formatTime(log.timestamp) }}</span>
            <span class="log-level">[{{ log.level.toUpperCase() }}]</span>
            <span class="log-message">{{ log.message }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { ctpService } from '../services/ctpService'
import { UserStorageService } from '../services/userStorage'
import { ConnectionStatus, LogEntry, MarketDataInfo } from '../types/ctp'
import { CtpTestHelper, TestResult } from '../utils/ctpTestHelper'

const mdStatus = ref<ConnectionStatus>('disconnected' as ConnectionStatus)
const traderStatus = ref<ConnectionStatus>('disconnected' as ConnectionStatus)
const testing = ref(false)
const logs = ref<LogEntry[]>([])
const marketData = ref<MarketDataInfo | null>(null)
const instruments = ref<any[]>([])

// 计算按交易所分组的合约数量
const instrumentsByExchange = computed(() => {
  const result: Record<string, number> = {}
  instruments.value.forEach(inst => {
    const exchange = inst.exchange_id
    result[exchange] = (result[exchange] || 0) + 1
  })
  return result
})

const mdStatusText = computed(() => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'login_success': '登录成功',
    'login_failed': '登录失败',
    'error': '错误'
  }
  return statusMap[mdStatus.value] || mdStatus.value
})

const traderStatusText = computed(() => {
  const statusMap = {
    'disconnected': '未连接',
    'connecting': '连接中',
    'connected': '已连接',
    'login_success': '登录成功',
    'login_failed': '登录失败',
    'error': '错误'
  }
  return statusMap[traderStatus.value] || traderStatus.value
})

const canSubscribe = computed(() => {
  return mdStatus.value === 'login_success'
})

const testConnection = async () => {
  testing.value = true
  try {
    // 获取默认配置
    const config = UserStorageService.getDefaultConfig()

    // 创建行情API
    const mdResult = await ctpService.createMdApi()
    if (mdResult.success) {
      addLog('创建行情API成功', 'info')
    } else {
      addLog(`创建行情API失败: ${mdResult.error}`, 'error')
      return
    }

    // 创建交易API
    const traderResult = await ctpService.createTraderApi()
    if (traderResult.success) {
      addLog('创建交易API成功', 'info')
    } else {
      addLog(`创建交易API失败: ${traderResult.error}`, 'error')
      return
    }

    // 行情登录
    const mdLoginResult = await ctpService.mdLogin(config)
    if (mdLoginResult.success) {
      addLog('行情登录成功', 'info')
    } else {
      addLog(`行情登录失败: ${mdLoginResult.error}`, 'error')
    }

    // 交易登录
    const traderLoginResult = await ctpService.traderLogin(config)
    if (traderLoginResult.success) {
      addLog('交易登录成功', 'info')
    } else {
      addLog(`交易登录失败: ${traderLoginResult.error}`, 'error')
    }

  } catch (error) {
    addLog(`测试连接异常: ${error}`, 'error')
  } finally {
    testing.value = false
  }
}

const runQuickTest = async () => {
  testing.value = true
  try {
    addLog('开始快速测试...', 'info')

    const testHelper = new CtpTestHelper()
    const results = await testHelper.runFullTest()
    const summary = testHelper.getTestSummary()

    addLog(`测试完成: 总计${summary.total}, 通过${summary.passed}, 失败${summary.failed}, 通过率${summary.passRate}`, 'info')

    // 显示详细结果
    results.forEach((result: TestResult) => {
      const level = result.success ? 'info' : 'error'
      const duration = result.duration ? ` (${result.duration}ms)` : ''
      addLog(`${result.success ? '✓' : '✗'} ${result.message}${duration}`, level)
    })

    const failedTests = testHelper.getFailedTests()
    if (failedTests.length > 0) {
      addLog(`发现${failedTests.length}个失败的测试，请检查详细日志`, 'warning')
    }

  } catch (error) {
    addLog(`快速测试异常: ${error}`, 'error')
  } finally {
    testing.value = false
  }
}

const subscribeMarketData = async () => {
  try {
    const result = await ctpService.subscribeMarketData(['rb2509'])
    if (result.success) {
      addLog('订阅rb2509行情成功', 'info')
    } else {
      addLog(`订阅行情失败: ${result.error}`, 'error')
    }
  } catch (error) {
    addLog(`订阅行情异常: ${error}`, 'error')
  }
}

const testInstruments = async () => {
  testing.value = true
  try {
    addLog('开始查询合约列表...', 'info')

    const result = await ctpService.queryInstruments()
    if (result.success && result.data) {
      instruments.value = result.data
      addLog(`查询合约成功，共获取到 ${result.data.length} 个合约`, 'info')

      // 按交易所统计
      const exchangeStats = instrumentsByExchange.value
      Object.entries(exchangeStats).forEach(([exchange, count]) => {
        addLog(`${exchange}: ${count} 个合约`, 'info')
      })

      // 显示前几个合约作为示例
      if (result.data.length > 0) {
        const examples = result.data.slice(0, 3)
        examples.forEach(inst => {
          addLog(`示例合约: ${inst.instrument_id} - ${inst.instrument_name} (${inst.exchange_id})`, 'info')
        })
      }
    } else {
      addLog(`查询合约失败: ${result.error || '未知错误'}`, 'error')
    }
  } catch (error) {
    addLog(`查询合约异常: ${error}`, 'error')
    console.error('查询合约异常:', error)
  } finally {
    testing.value = false
  }
}

const addLog = (message: string, level: string) => {
  const log: LogEntry = {
    timestamp: new Date().toISOString(),
    level: level as any,
    message
  }
  logs.value.unshift(log)
  if (logs.value.length > 100) {
    logs.value.pop()
  }
}

const clearLogs = () => {
  logs.value = []
}

const formatTime = (timestamp: string) => {
  return new Date(timestamp).toLocaleTimeString()
}

onMounted(() => {
  // 监听状态变化
  ctpService.on('md_status_change', (status: ConnectionStatus) => {
    mdStatus.value = status
  })
  
  ctpService.on('trader_status_change', (status: ConnectionStatus) => {
    traderStatus.value = status
  })
  
  ctpService.on('log', (log: LogEntry) => {
    logs.value.unshift(log)
    if (logs.value.length > 100) {
      logs.value.pop()
    }
  })
  
  ctpService.on('market_data', (data: MarketDataInfo) => {
    if (data.instrument_id === 'rb2509') {
      marketData.value = data
      addLog(`收到rb2509行情: 价格=${data.last_price}, 成交量=${data.volume}`, 'info')
    }
  })
})

onUnmounted(() => {
  // 清理事件监听器
  ctpService.off('md_status_change', () => {})
  ctpService.off('trader_status_change', () => {})
  ctpService.off('log', () => {})
  ctpService.off('market_data', () => {})
})
</script>

<style scoped>
.ctp-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-panel {
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.test-panel h2 {
  margin-top: 0;
  color: #333;
}

.test-panel h3 {
  color: #666;
  border-bottom: 1px solid #eee;
  padding-bottom: 8px;
}

.status-section, .action-section, .market-data-section, .log-section {
  margin-bottom: 30px;
}

.status-item, .data-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.status {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
}

.status.disconnected { background: #f5f5f5; color: #999; }
.status.connecting { background: #fff7e6; color: #fa8c16; }
.status.connected { background: #f6ffed; color: #52c41a; }
.status.login_success { background: #f6ffed; color: #52c41a; }
.status.login_failed { background: #fff2f0; color: #ff4d4f; }
.status.error { background: #fff2f0; color: #ff4d4f; }

.button-group {
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
}

.button-group button {
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  background: #fff;
  cursor: pointer;
  transition: all 0.3s;
}

.button-group button:hover:not(:disabled) {
  border-color: #40a9ff;
  color: #40a9ff;
}

.button-group button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.market-data {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 10px;
}

.log-container {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  padding: 10px;
  background: #fafafa;
}

.log-item {
  display: flex;
  gap: 10px;
  padding: 4px 0;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.log-time {
  color: #999;
  min-width: 80px;
}

.log-level {
  min-width: 60px;
  font-weight: bold;
}

.log-level.info { color: #1890ff; }
.log-level.warning { color: #fa8c16; }
.log-level.error { color: #ff4d4f; }
.log-level.debug { color: #999; }

.log-message {
  flex: 1;
}

/* 合约数据样式 */
.instruments-section {
  margin: 20px 0;
}

.instruments-container {
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
}

.instruments-summary {
  background: #f5f5f5;
  padding: 15px;
  border-bottom: 1px solid #d9d9d9;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.summary-item span:first-child {
  color: #666;
}

.summary-item span:last-child {
  font-weight: bold;
  color: #1890ff;
}

.instruments-list {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
}

.instrument-item {
  display: flex;
  gap: 15px;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
  font-size: 13px;
}

.instrument-item:last-child {
  border-bottom: none;
}

.instrument-id {
  min-width: 80px;
  font-weight: bold;
  color: #1890ff;
}

.instrument-name {
  flex: 1;
  color: #333;
}

.instrument-exchange {
  min-width: 60px;
  color: #666;
  font-size: 12px;
}

.more-instruments {
  text-align: center;
  padding: 10px;
  color: #999;
  font-style: italic;
}
</style>
