impl :: bincode :: Encode for CThostFtdcQryProductField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode
        :: Encode :: encode(&self.ProductClass, encoder) ?; :: bincode ::
        Encode :: encode(&self.ExchangeID, encoder) ?; :: bincode :: Encode ::
        encode(&self.ProductID, encoder) ?; core :: result :: Result :: Ok(())
    }
}