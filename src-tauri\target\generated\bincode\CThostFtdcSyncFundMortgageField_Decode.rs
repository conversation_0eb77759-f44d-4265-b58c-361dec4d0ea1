impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncFundMortgageField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            MortgageSeqNo : :: bincode :: Decode :: decode(decoder) ?,
            BrokerID : :: bincode :: Decode :: decode(decoder) ?, InvestorID :
            :: bincode :: Decode :: decode(decoder) ?, FromCurrencyID : ::
            bincode :: Decode :: decode(decoder) ?, MortgageAmount : ::
            bincode :: Decode :: decode(decoder) ?, ToCurrencyID : :: bincode
            :: Decode :: decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncFundMortgageField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            MortgageSeqNo : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, FromCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MortgageAmount : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ToCurrencyID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}