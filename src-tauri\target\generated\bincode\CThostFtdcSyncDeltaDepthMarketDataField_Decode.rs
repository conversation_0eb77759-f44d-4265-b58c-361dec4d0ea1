impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcSyncDeltaDepthMarketDataField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: Decode :: decode(decoder) ?,
            InstrumentID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeID : :: bincode :: Decode :: decode(decoder) ?,
            ExchangeInstID : :: bincode :: Decode :: decode(decoder) ?,
            LastPrice : :: bincode :: Decode :: decode(decoder) ?,
            PreSettlementPrice : :: bincode :: Decode :: decode(decoder) ?,
            PreClosePrice : :: bincode :: Decode :: decode(decoder) ?,
            PreOpenInterest : :: bincode :: Decode :: decode(decoder) ?,
            OpenPrice : :: bincode :: Decode :: decode(decoder) ?,
            HighestPrice : :: bincode :: Decode :: decode(decoder) ?,
            LowestPrice : :: bincode :: Decode :: decode(decoder) ?, Volume :
            :: bincode :: Decode :: decode(decoder) ?, Turnover : :: bincode
            :: Decode :: decode(decoder) ?, OpenInterest : :: bincode ::
            Decode :: decode(decoder) ?, ClosePrice : :: bincode :: Decode ::
            decode(decoder) ?, SettlementPrice : :: bincode :: Decode ::
            decode(decoder) ?, UpperLimitPrice : :: bincode :: Decode ::
            decode(decoder) ?, LowerLimitPrice : :: bincode :: Decode ::
            decode(decoder) ?, PreDelta : :: bincode :: Decode ::
            decode(decoder) ?, CurrDelta : :: bincode :: Decode ::
            decode(decoder) ?, UpdateTime : :: bincode :: Decode ::
            decode(decoder) ?, UpdateMillisec : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice1 : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume1 : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice1 : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume1 : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice2 : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume2 : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice2 : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume2 : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice3 : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume3 : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice3 : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume3 : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice4 : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume4 : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice4 : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume4 : :: bincode :: Decode ::
            decode(decoder) ?, BidPrice5 : :: bincode :: Decode ::
            decode(decoder) ?, BidVolume5 : :: bincode :: Decode ::
            decode(decoder) ?, AskPrice5 : :: bincode :: Decode ::
            decode(decoder) ?, AskVolume5 : :: bincode :: Decode ::
            decode(decoder) ?, AveragePrice : :: bincode :: Decode ::
            decode(decoder) ?, ActionDay : :: bincode :: Decode ::
            decode(decoder) ?, BandingUpperPrice : :: bincode :: Decode ::
            decode(decoder) ?, BandingLowerPrice : :: bincode :: Decode ::
            decode(decoder) ?, ActionDirection : :: bincode :: Decode ::
            decode(decoder) ?, SyncDeltaSequenceNo : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcSyncDeltaDepthMarketDataField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, InstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchangeID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ExchangeInstID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LastPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PreSettlementPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PreClosePrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PreOpenInterest : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OpenPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, HighestPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LowestPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Volume : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, Turnover : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            OpenInterest : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, ClosePrice : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, SettlementPrice :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UpperLimitPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            LowerLimitPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, PreDelta : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, CurrDelta : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, UpdateTime : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, UpdateMillisec :
            :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidPrice1 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume1 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice1 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskVolume1 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidPrice2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume2 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskVolume2 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidPrice3 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice3 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskVolume3 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidPrice4 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume4 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice4 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskVolume4 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BidPrice5 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, BidVolume5 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AskPrice5 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, AskVolume5 : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, AveragePrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ActionDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BandingUpperPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            BandingLowerPrice : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, ActionDirection : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SyncDeltaSequenceNo : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?,
        })
    }
}