impl :: bincode :: Encode for CThostFtdcAuthenticationInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.BrokerID, encoder) ?; :: bincode
        :: Encode :: encode(&self.UserID, encoder) ?; :: bincode :: Encode ::
        encode(&self.UserProductInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.AuthInfo, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON>, encoder) ?; :: bincode :: Encode ::
        encode(&self.AppID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AppType, encoder) ?; :: bincode :: Encode ::
        encode(&self.reserve1, encoder) ?; :: bincode :: Encode ::
        encode(&self.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}