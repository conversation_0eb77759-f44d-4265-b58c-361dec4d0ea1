impl :: bincode :: Encode for CThostFtdcMulticastInstrumentField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TopicID, encoder) ?; :: bincode
        :: Encode :: encode(&self.reserve1, encoder) ?; :: bincode :: Encode
        :: encode(&self.InstrumentNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.CodePrice, encoder) ?; :: bincode :: Encode ::
        encode(&self.VolumeMultiple, encoder) ?; :: bincode :: Encode ::
        encode(&self.PriceTick, encoder) ?; :: bincode :: Encode ::
        encode(&self.InstrumentID, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}