impl < __Context > :: bincode :: Decode < __Context > for
CThostFtdcInvestorPositionDetailField
{
    fn decode < __D : :: bincode :: de :: Decoder < Context = __Context > >
    (decoder : & mut __D) ->core :: result :: Result < Self, :: bincode ::
    error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: Decode :: decode(decoder) ?, BrokerID :
            :: bincode :: Decode :: decode(decoder) ?, InvestorID : :: bincode
            :: Decode :: decode(decoder) ?, HedgeFlag : :: bincode :: Decode
            :: decode(decoder) ?, Direction : :: bincode :: Decode ::
            decode(decoder) ?, OpenDate : :: bincode :: Decode ::
            decode(decoder) ?, TradeID : :: bincode :: Decode ::
            decode(decoder) ?, Volume : :: bincode :: Decode ::
            decode(decoder) ?, OpenPrice : :: bincode :: Decode ::
            decode(decoder) ?, TradingDay : :: bincode :: Decode ::
            decode(decoder) ?, SettlementID : :: bincode :: Decode ::
            decode(decoder) ?, TradeType : :: bincode :: Decode ::
            decode(decoder) ?, reserve2 : :: bincode :: Decode ::
            decode(decoder) ?, ExchangeID : :: bincode :: Decode ::
            decode(decoder) ?, CloseProfitByDate : :: bincode :: Decode ::
            decode(decoder) ?, CloseProfitByTrade : :: bincode :: Decode ::
            decode(decoder) ?, PositionProfitByDate : :: bincode :: Decode ::
            decode(decoder) ?, PositionProfitByTrade : :: bincode :: Decode ::
            decode(decoder) ?, Margin : :: bincode :: Decode ::
            decode(decoder) ?, ExchMargin : :: bincode :: Decode ::
            decode(decoder) ?, MarginRateByMoney : :: bincode :: Decode ::
            decode(decoder) ?, MarginRateByVolume : :: bincode :: Decode ::
            decode(decoder) ?, LastSettlementPrice : :: bincode :: Decode ::
            decode(decoder) ?, SettlementPrice : :: bincode :: Decode ::
            decode(decoder) ?, CloseVolume : :: bincode :: Decode ::
            decode(decoder) ?, CloseAmount : :: bincode :: Decode ::
            decode(decoder) ?, TimeFirstVolume : :: bincode :: Decode ::
            decode(decoder) ?, InvestUnitID : :: bincode :: Decode ::
            decode(decoder) ?, SpecPosiType : :: bincode :: Decode ::
            decode(decoder) ?, InstrumentID : :: bincode :: Decode ::
            decode(decoder) ?, CombInstrumentID : :: bincode :: Decode ::
            decode(decoder) ?,
        })
    }
} impl < '__de, __Context > :: bincode :: BorrowDecode < '__de, __Context >
for CThostFtdcInvestorPositionDetailField
{
    fn borrow_decode < __D : :: bincode :: de :: BorrowDecoder < '__de,
    Context = __Context > > (decoder : & mut __D) ->core :: result :: Result <
    Self, :: bincode :: error :: DecodeError >
    {
        core :: result :: Result ::
        Ok(Self
        {
            reserve1 : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, BrokerID : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, InvestorID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, HedgeFlag : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, Direction : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, OpenDate : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, TradeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, Volume : :: bincode :: BorrowDecode ::<
            '_, __Context >:: borrow_decode(decoder) ?, OpenPrice : :: bincode
            :: BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TradingDay : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SettlementID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            TradeType : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, reserve2 : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, ExchangeID : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CloseProfitByDate : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            CloseProfitByTrade : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, PositionProfitByDate : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            PositionProfitByTrade : :: bincode :: BorrowDecode ::< '_,
            __Context >:: borrow_decode(decoder) ?, Margin : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            ExchMargin : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, MarginRateByMoney : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            MarginRateByVolume : :: bincode :: BorrowDecode ::< '_, __Context
            >:: borrow_decode(decoder) ?, LastSettlementPrice : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            SettlementPrice : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CloseVolume : :: bincode :: BorrowDecode
            ::< '_, __Context >:: borrow_decode(decoder) ?, CloseAmount : ::
            bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, TimeFirstVolume : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InvestUnitID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, SpecPosiType : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
            InstrumentID : :: bincode :: BorrowDecode ::< '_, __Context >::
            borrow_decode(decoder) ?, CombInstrumentID : :: bincode ::
            BorrowDecode ::< '_, __Context >:: borrow_decode(decoder) ?,
        })
    }
}