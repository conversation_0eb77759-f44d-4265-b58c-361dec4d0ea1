impl :: bincode :: Encode for CThostFtdcAccountregisterField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.TradeDay, encoder) ?; :: bincode
        :: Encode :: encode(&self.BankID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankBranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankAccount, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerID, encoder) ?; :: bincode :: Encode ::
        encode(&self.BrokerBranchID, encoder) ?; :: bincode :: Encode ::
        encode(&self.AccountID, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdCardType, encoder) ?; :: bincode :: Encode ::
        encode(&self.IdentifiedCardNo, encoder) ?; :: bincode :: Encode ::
        encode(&self.CustomerName, encoder) ?; :: bincode :: Encode ::
        encode(&self.CurrencyID, encoder) ?; :: bincode :: Encode ::
        encode(&self.OpenOrDestroy, encoder) ?; :: bincode :: Encode ::
        encode(&self.RegDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.OutDate, encoder) ?; :: bincode :: Encode ::
        encode(&self.TID, encoder) ?; :: bincode :: Encode ::
        encode(&self.CustType, encoder) ?; :: bincode :: Encode ::
        encode(&self.BankAccType, encoder) ?; :: bincode :: Encode ::
        encode(&self.LongCustomerName, encoder) ?; core :: result :: Result ::
        Ok(())
    }
}