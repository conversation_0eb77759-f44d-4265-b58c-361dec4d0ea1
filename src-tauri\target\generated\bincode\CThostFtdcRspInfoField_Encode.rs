impl :: bincode :: Encode for CThostFtdcRspInfoField
{
    fn encode < __E : :: bincode :: enc :: Encoder >
    (& self, encoder : & mut __E) ->core :: result :: Result < (), :: bincode
    :: error :: EncodeError >
    {
        :: bincode :: Encode :: encode(&self.ErrorID, encoder) ?; :: bincode
        :: Encode :: encode(&self.ErrorMsg, encoder) ?; core :: result ::
        Result :: Ok(())
    }
}